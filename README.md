# 通用离线应用软件注册机

## 项目概括
本项目旨在开发一个基于C# WPF的通用离线软件注册机桌面应用程序。该工具用于处理设备指纹信息，生成和验证软件授权文件(License)，支持多种授权类型和功能权限控制，并提供批量操作功能。适用于软件开发商进行离线License管理和分发。

## 技术选型
- **主要编程语言**: C# (.NET 6.0+)
- **UI框架**: WPF (Windows Presentation Foundation) + XAML
- **加密算法**: RSA (非对称加密) + AES (对称加密)
- **关键库/框架**: 
  - System.Security.Cryptography (加密)
  - Newtonsoft.Json (JSON序列化)
  - System.Management (硬件信息获取)
  - Microsoft.Extensions.Logging (日志记录)
  - System.Configuration (配置管理)
- **数据存储**: XML/JSON文件 + SQLite (可选)
- **版本控制**: Git
- **其他工具**: Visual Studio 2022, NuGet包管理

## 项目结构 / 模块划分
```
LicenseGenerator/
├── LicenseGenerator.WPF/              # WPF主应用程序
│   ├── Views/                         # XAML视图文件
│   ├── ViewModels/                    # MVVM视图模型
│   ├── Controls/                      # 自定义用户控件
│   ├── Resources/                     # 资源文件(图标、样式等)
│   └── App.xaml                       # 应用程序入口
├── LicenseGenerator.Core/             # 核心业务逻辑库
│   ├── Models/                        # 数据模型
│   ├── Services/                      # 业务服务
│   ├── Interfaces/                    # 接口定义
│   └── Utilities/                     # 工具类
├── LicenseGenerator.Crypto/           # 加密模块
│   ├── RSAProvider.cs                 # RSA加密提供者
│   ├── AESProvider.cs                 # AES加密提供者
│   ├── DigitalSignature.cs            # 数字签名
│   └── KeyManager.cs                  # 密钥管理
├── LicenseGenerator.DeviceInfo/       # 设备指纹模块
│   ├── DeviceFingerprintCollector.cs  # 设备指纹收集器
│   ├── HardwareInfoProvider.cs        # 硬件信息提供者
│   └── DeviceIdentifier.cs            # 设备标识符
├── LicenseGenerator.Data/             # 数据访问层
│   ├── Repositories/                  # 数据仓储
│   ├── Entities/                      # 数据实体
│   └── DbContext/                     # 数据库上下文
├── Tests/                             # 单元测试项目
│   ├── LicenseGenerator.Core.Tests/
│   ├── LicenseGenerator.Crypto.Tests/
│   └── LicenseGenerator.WPF.Tests/
├── Docs/                              # 文档目录
├── Config/                            # 配置文件目录
└── LicenseGenerator.sln               # 解决方案文件
```

## 核心功能 / 模块详解
- **设备指纹处理模块**: 接收、解析和验证客户提供的设备指纹信息，支持多种硬件标识符格式(CPU ID、主板序列号、MAC地址、硬盘序列号等)。
- **License生成引擎**: 基于设备指纹和用户配置生成包含应用标识、授权类型、功能列表、有效期的加密License文件。
- **License验证器**: 验证License文件的数字签名、有效期、设备绑定和功能权限的完整性。
- **批量操作管理**: 支持Excel/CSV导入设备信息，批量生成License文件，并提供进度跟踪和错误处理。
- **加密安全模块**: 实现RSA公私钥对管理、AES对称加密、数字签名生成与验证，确保License安全性。
- **用户界面管理**: 基于MVVM模式的WPF界面，提供直观的操作流程和实时状态反馈。
- **配置管理系统**: 管理应用程序配置、加密密钥、授权模板和日志设置。
- **日志与审计**: 记录所有关键操作、错误信息和安全事件，支持日志导出和分析。

## 数据模型
- **LicenseInfo**: { Id (GUID), AppId (string), DeviceFingerprint (string), LicenseType (enum), Features (List<string>), IssueDate (DateTime), ExpiryDate (DateTime?), DigitalSignature (string) }
- **DeviceInfo**: { DeviceId (string), CpuId (string), MotherboardSerial (string), MacAddresses (List<string>), HardDiskSerial (string), CollectedAt (DateTime) }
- **AppConfig**: { AppId (string), AppName (string), AvailableFeatures (List<string>), DefaultLicenseType (enum), MaxValidityDays (int) }
- **CryptoKeys**: { PublicKey (string), PrivateKey (string), KeySize (int), CreatedAt (DateTime), IsActive (bool) }

## 技术实现细节

### 项目结构搭建 {#project-structure}
**实现时间**: 2024年6月11日
**技术方案**: 基于.NET 6.0的多项目解决方案架构

**核心组件**:
- **LicenseGenerator.sln**: 主解决方案文件，管理所有子项目
- **LicenseGenerator.WPF**: WPF主应用程序项目，采用MVVM架构模式
- **LicenseGenerator.Core**: 核心业务逻辑库，包含服务层和模型定义
- **LicenseGenerator.Crypto**: 独立的加密模块，负责RSA/AES加密和数字签名
- **LicenseGenerator.DeviceInfo**: 设备指纹收集模块，处理硬件信息获取
- **LicenseGenerator.Data**: 数据访问层，支持文件和数据库存储

**项目依赖关系**:
```
LicenseGenerator.WPF
└── LicenseGenerator.Core
    ├── LicenseGenerator.Crypto
    ├── LicenseGenerator.DeviceInfo
    └── LicenseGenerator.Data
```

**测试架构**:
- 采用xUnit测试框架
- 为Core和Crypto模块创建独立测试项目
- 支持单元测试和集成测试

**关键配置**:
- 目标框架: .NET 6.0 (类库) / .NET 6.0-windows (WPF)
- 项目引用已正确配置，确保依赖关系清晰
- 编译验证通过，所有项目可正常构建

**目录结构**:
- `/Config`: 配置文件存储目录
- `/Docs`: 项目文档目录
- `/Tests`: 测试项目统一目录
- `.gitignore`: 完整的.NET项目忽略规则

### 数据模型定义 {#data-models}
**实现时间**: 2024年6月11日
**技术方案**: 基于C#类和枚举的强类型数据模型设计

**核心数据模型**:
- **LicenseInfo**: 完整的License信息模型，包含ID、应用绑定、设备指纹、授权类型、功能列表、有效期、客户信息、数字签名等核心字段
- **CustomerInfo**: 客户信息模型，支持个人和企业客户，包含联系方式、地址、客户类型等信息
- **DeviceInfo**: 设备指纹模型，收集CPU ID、主板序列号、MAC地址、硬盘序列号、内存、操作系统、BIOS等硬件信息
- **AppConfig**: 应用程序配置模型，定义可用功能、License模板、默认设置等
- **CryptoKeys**: 加密密钥模型，管理RSA公私钥对、密钥状态、使用统计等
- **BatchOperation**: 批量操作模型，支持批量License生成、验证、撤销等操作的状态跟踪

**数据传输对象(DTOs)**:
- **LicenseGenerationRequest/Response**: License生成的请求和响应模型
- **LicenseValidationRequest/Response**: License验证的请求和响应模型
- **BatchOperationRequest/Response**: 批量操作的请求和响应模型
- **BatchImportItem**: 批量导入数据项模型

**枚举定义**:
- **LicenseType**: Trial, Standard, Professional, Enterprise, Developer, Custom
- **LicenseStatus**: Active, Suspended, Revoked, Expired
- **CustomerType**: Individual, SmallBusiness, MediumBusiness, Enterprise, Educational, Government, NonProfit
- **DeviceTrustLevel**: Unknown, Low, Medium, High, Verified
- **CryptoAlgorithm**: RSA, ECC, DSA
- **KeyUsage**: Signing, Encryption, SigningAndEncryption, KeyExchange
- **BatchOperationType**: LicenseGeneration, LicenseValidation, LicenseRevocation, DeviceFingerprintImport, DataExport

**设计特点**:
- 使用Data Annotations进行数据验证
- 支持复杂的嵌套对象关系
- 提供计算属性和业务逻辑方法
- 完整的枚举类型定义
- 支持可选字段和默认值设置
- 包含审计字段(创建时间、更新时间等)

## 开发状态跟踪
| 模块/功能                | 状态     | 负责人 | 计划完成日期 | 实际完成日期 | 备注与链接 |
|--------------------------|----------|--------|--------------|--------------|-----------| 
| 项目结构搭建             | 已完成   | AI     | 2024-01-15   | 2024-06-11   | [详见技术实现](#project-structure) |
| 数据模型定义             | 已完成   | AI     | 2024-01-16   | 2024-06-11   | [详见技术实现](#data-models) |
| 加密安全模块             | 未开始   | AI     | 2024-01-18   |              |           |
| 设备指纹处理模块         | 未开始   | AI     | 2024-01-20   |              |           |
| License生成引擎          | 未开始   | AI     | 2024-01-22   |              |           |
| License验证器            | 未开始   | AI     | 2024-01-24   |              |           |
| WPF用户界面              | 未开始   | AI     | 2024-01-26   |              |           |
| 批量操作管理             | 未开始   | AI     | 2024-01-28   |              |           |
| 配置管理系统             | 未开始   | AI     | 2024-01-30   |              |           |
| 日志与审计模块           | 未开始   | AI     | 2024-02-01   |              |           |

## 代码检查与问题记录
[本部分用于记录代码检查结果和开发过程中遇到的问题及其解决方案。]

## 环境设置与运行指南
### 开发环境要求
- Visual Studio 2022 (Community版本或更高)
- .NET 6.0 SDK 或更高版本
- Windows 10/11 操作系统

### 依赖包安装
```powershell
# 在项目根目录执行
dotnet restore
```

### 运行项目
```powershell
# 编译并运行WPF应用
dotnet run --project LicenseGenerator.WPF
```

### 测试运行
```powershell
# 运行所有单元测试
dotnet test
```
