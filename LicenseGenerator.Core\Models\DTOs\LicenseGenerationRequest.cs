using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;

namespace LicenseGenerator.Core.Models.DTOs
{
    /// <summary>
    /// License生成请求模型
    /// </summary>
    public class LicenseGenerationRequest
    {
        /// <summary>
        /// 应用程序标识符
        /// </summary>
        [Required]
        [StringLength(100)]
        public string AppId { get; set; } = string.Empty;

        /// <summary>
        /// 设备指纹信息
        /// </summary>
        [Required]
        public string DeviceFingerprint { get; set; } = string.Empty;

        /// <summary>
        /// 客户信息
        /// </summary>
        [Required]
        public CustomerInfo Customer { get; set; } = new CustomerInfo();

        /// <summary>
        /// License类型
        /// </summary>
        public LicenseType LicenseType { get; set; } = LicenseType.Trial;

        /// <summary>
        /// 授权功能列表
        /// </summary>
        public List<string> Features { get; set; } = new List<string>();

        /// <summary>
        /// 有效期天数 (null表示永不过期)
        /// </summary>
        public int? ValidityDays { get; set; }

        /// <summary>
        /// 最大并发用户数
        /// </summary>
        public int MaxConcurrentUsers { get; set; } = 1;

        /// <summary>
        /// 使用的密钥ID
        /// </summary>
        public Guid? KeyId { get; set; }

        /// <summary>
        /// 备注信息
        /// </summary>
        [StringLength(1000)]
        public string Notes { get; set; } = string.Empty;

        /// <summary>
        /// 输出文件路径 (可选，用于指定生成的License文件保存位置)
        /// </summary>
        [StringLength(500)]
        public string OutputFilePath { get; set; } = string.Empty;
    }

    /// <summary>
    /// License生成响应模型
    /// </summary>
    public class LicenseGenerationResponse
    {
        /// <summary>
        /// 是否成功
        /// </summary>
        public bool Success { get; set; }

        /// <summary>
        /// 生成的License信息
        /// </summary>
        public LicenseInfo? LicenseInfo { get; set; }

        /// <summary>
        /// License文件内容 (Base64编码)
        /// </summary>
        public string LicenseFileContent { get; set; } = string.Empty;

        /// <summary>
        /// 输出文件路径
        /// </summary>
        public string OutputFilePath { get; set; } = string.Empty;

        /// <summary>
        /// 错误信息
        /// </summary>
        public string ErrorMessage { get; set; } = string.Empty;

        /// <summary>
        /// 警告信息列表
        /// </summary>
        public List<string> Warnings { get; set; } = new List<string>();

        /// <summary>
        /// 生成耗时 (毫秒)
        /// </summary>
        public long GenerationTimeMs { get; set; }

        /// <summary>
        /// 生成时间
        /// </summary>
        public DateTime GeneratedAt { get; set; } = DateTime.UtcNow;
    }

    /// <summary>
    /// License验证请求模型
    /// </summary>
    public class LicenseValidationRequest
    {
        /// <summary>
        /// License文件内容或路径
        /// </summary>
        [Required]
        public string LicenseData { get; set; } = string.Empty;

        /// <summary>
        /// 当前设备指纹 (用于验证设备绑定)
        /// </summary>
        public string CurrentDeviceFingerprint { get; set; } = string.Empty;

        /// <summary>
        /// 应用程序标识符 (用于验证License是否适用于当前应用)
        /// </summary>
        [StringLength(100)]
        public string AppId { get; set; } = string.Empty;

        /// <summary>
        /// 验证选项
        /// </summary>
        public LicenseValidationOptions Options { get; set; } = new LicenseValidationOptions();
    }

    /// <summary>
    /// License验证选项
    /// </summary>
    public class LicenseValidationOptions
    {
        /// <summary>
        /// 是否验证设备绑定
        /// </summary>
        public bool ValidateDeviceBinding { get; set; } = true;

        /// <summary>
        /// 是否验证过期时间
        /// </summary>
        public bool ValidateExpiry { get; set; } = true;

        /// <summary>
        /// 是否验证数字签名
        /// </summary>
        public bool ValidateSignature { get; set; } = true;

        /// <summary>
        /// 是否验证应用程序绑定
        /// </summary>
        public bool ValidateAppBinding { get; set; } = true;

        /// <summary>
        /// 允许的时间偏差 (分钟)
        /// </summary>
        public int TimeToleranceMinutes { get; set; } = 5;
    }

    /// <summary>
    /// License验证响应模型
    /// </summary>
    public class LicenseValidationResponse
    {
        /// <summary>
        /// 验证是否通过
        /// </summary>
        public bool IsValid { get; set; }

        /// <summary>
        /// License信息
        /// </summary>
        public LicenseInfo? LicenseInfo { get; set; }

        /// <summary>
        /// 验证结果详情
        /// </summary>
        public List<ValidationResult> ValidationResults { get; set; } = new List<ValidationResult>();

        /// <summary>
        /// 错误信息
        /// </summary>
        public string ErrorMessage { get; set; } = string.Empty;

        /// <summary>
        /// 验证时间
        /// </summary>
        public DateTime ValidatedAt { get; set; } = DateTime.UtcNow;

        /// <summary>
        /// 验证耗时 (毫秒)
        /// </summary>
        public long ValidationTimeMs { get; set; }
    }

    /// <summary>
    /// 验证结果详情
    /// </summary>
    public class ValidationResult
    {
        /// <summary>
        /// 验证项目名称
        /// </summary>
        public string ValidationItem { get; set; } = string.Empty;

        /// <summary>
        /// 验证是否通过
        /// </summary>
        public bool Passed { get; set; }

        /// <summary>
        /// 验证消息
        /// </summary>
        public string Message { get; set; } = string.Empty;

        /// <summary>
        /// 验证级别
        /// </summary>
        public ValidationLevel Level { get; set; } = ValidationLevel.Error;
    }

    /// <summary>
    /// 验证级别枚举
    /// </summary>
    public enum ValidationLevel
    {
        /// <summary>
        /// 信息
        /// </summary>
        Information = 0,

        /// <summary>
        /// 警告
        /// </summary>
        Warning = 1,

        /// <summary>
        /// 错误
        /// </summary>
        Error = 2,

        /// <summary>
        /// 严重错误
        /// </summary>
        Critical = 3
    }
}
