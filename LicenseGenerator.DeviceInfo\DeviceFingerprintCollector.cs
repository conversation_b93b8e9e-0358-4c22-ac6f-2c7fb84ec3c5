using System;
using System.Collections.Generic;
using System.Linq;
using System.Security.Cryptography;
using System.Text;
using System.Text.Json;

namespace LicenseGenerator.DeviceInfo
{
    /// <summary>
    /// 设备指纹收集器 - 负责收集和生成设备指纹
    /// </summary>
    public class DeviceFingerprintCollector
    {
        private readonly DeviceFingerprintOptions _options;

        /// <summary>
        /// 初始化设备指纹收集器
        /// </summary>
        /// <param name="options">收集选项</param>
        public DeviceFingerprintCollector(DeviceFingerprintOptions? options = null)
        {
            _options = options ?? new DeviceFingerprintOptions();
        }

        /// <summary>
        /// 收集完整的设备信息
        /// </summary>
        /// <returns>设备信息</returns>
        public DeviceInformation CollectDeviceInformation()
        {
            var deviceInfo = new DeviceInformation
            {
                CollectedAt = DateTime.UtcNow,
                FingerprintVersion = _options.FingerprintVersion,
                IsVirtualMachine = HardwareInfoProvider.IsVirtualMachine()
            };

            try
            {
                // 收集CPU信息
                if (_options.IncludeCpuInfo)
                {
                    deviceInfo.CpuInfo = HardwareInfoProvider.GetCpuInfo();
                }

                // 收集主板信息
                if (_options.IncludeMotherboardInfo)
                {
                    deviceInfo.MotherboardInfo = HardwareInfoProvider.GetMotherboardInfo();
                }

                // 收集内存信息
                if (_options.IncludeMemoryInfo)
                {
                    deviceInfo.MemoryInfo = HardwareInfoProvider.GetMemoryInfo();
                }

                // 收集硬盘信息
                if (_options.IncludeDiskInfo)
                {
                    deviceInfo.DiskInfoList = HardwareInfoProvider.GetDiskInfo();
                }

                // 收集网络适配器信息
                if (_options.IncludeNetworkInfo)
                {
                    deviceInfo.NetworkAdapterInfoList = HardwareInfoProvider.GetNetworkAdapterInfo();
                }

                // 收集BIOS信息
                if (_options.IncludeBiosInfo)
                {
                    deviceInfo.BiosInfo = HardwareInfoProvider.GetBiosInfo();
                }

                // 收集操作系统信息
                if (_options.IncludeOsInfo)
                {
                    deviceInfo.OperatingSystemInfo = HardwareInfoProvider.GetOperatingSystemInfo();
                }

                // 收集系统UUID
                if (_options.IncludeSystemUuid)
                {
                    deviceInfo.SystemUuid = HardwareInfoProvider.GetSystemUuid();
                }

                // 生成设备指纹
                deviceInfo.DeviceFingerprint = GenerateDeviceFingerprint(deviceInfo);
                
                // 评估信任级别
                deviceInfo.TrustLevel = EvaluateTrustLevel(deviceInfo);

            }
            catch (Exception ex)
            {
                deviceInfo.ErrorMessage = ex.Message;
            }

            return deviceInfo;
        }

        /// <summary>
        /// 生成设备指纹
        /// </summary>
        /// <param name="deviceInfo">设备信息</param>
        /// <returns>设备指纹</returns>
        public string GenerateDeviceFingerprint(DeviceInformation deviceInfo)
        {
            var fingerprintData = new List<string>();

            // 添加CPU信息
            if (deviceInfo.CpuInfo != null && !string.IsNullOrEmpty(deviceInfo.CpuInfo.ProcessorId))
            {
                fingerprintData.Add($"CPU:{deviceInfo.CpuInfo.ProcessorId}");
            }

            // 添加主板信息
            if (deviceInfo.MotherboardInfo != null && !string.IsNullOrEmpty(deviceInfo.MotherboardInfo.SerialNumber))
            {
                fingerprintData.Add($"MB:{deviceInfo.MotherboardInfo.SerialNumber}");
            }

            // 添加主要硬盘序列号
            var primaryDisk = deviceInfo.DiskInfoList?.FirstOrDefault(d => !string.IsNullOrEmpty(d.SerialNumber));
            if (primaryDisk != null)
            {
                fingerprintData.Add($"DISK:{primaryDisk.SerialNumber}");
            }

            // 添加主要网络适配器MAC地址
            var primaryAdapter = deviceInfo.NetworkAdapterInfoList?
                .Where(n => !string.IsNullOrEmpty(n.MacAddress) && n.MacAddress != "000000000000")
                .OrderBy(n => n.Name)
                .FirstOrDefault();
            if (primaryAdapter != null)
            {
                fingerprintData.Add($"MAC:{primaryAdapter.MacAddress}");
            }

            // 添加BIOS信息
            if (deviceInfo.BiosInfo != null && !string.IsNullOrEmpty(deviceInfo.BiosInfo.SerialNumber))
            {
                fingerprintData.Add($"BIOS:{deviceInfo.BiosInfo.SerialNumber}");
            }

            // 添加系统UUID
            if (!string.IsNullOrEmpty(deviceInfo.SystemUuid))
            {
                fingerprintData.Add($"UUID:{deviceInfo.SystemUuid}");
            }

            // 如果没有收集到足够的信息，使用备用方法
            if (fingerprintData.Count < _options.MinimumFingerprintComponents)
            {
                // 添加机器名
                fingerprintData.Add($"MACHINE:{Environment.MachineName}");
                
                // 添加用户域
                fingerprintData.Add($"DOMAIN:{Environment.UserDomainName}");
                
                // 添加处理器数量
                fingerprintData.Add($"CORES:{Environment.ProcessorCount}");
            }

            // 组合指纹数据并生成哈希
            var combinedData = string.Join("|", fingerprintData.OrderBy(x => x));
            return GenerateHash(combinedData);
        }

        /// <summary>
        /// 从字符串生成设备指纹
        /// </summary>
        /// <param name="fingerprintString">指纹字符串</param>
        /// <returns>设备指纹哈希</returns>
        public static string GenerateDeviceFingerprintFromString(string fingerprintString)
        {
            return GenerateHash(fingerprintString);
        }

        /// <summary>
        /// 验证设备指纹
        /// </summary>
        /// <param name="storedFingerprint">存储的指纹</param>
        /// <param name="currentDeviceInfo">当前设备信息</param>
        /// <returns>验证结果</returns>
        public FingerprintValidationResult ValidateFingerprint(string storedFingerprint, DeviceInformation? currentDeviceInfo = null)
        {
            var result = new FingerprintValidationResult
            {
                StoredFingerprint = storedFingerprint,
                ValidationTime = DateTime.UtcNow
            };

            try
            {
                // 如果没有提供当前设备信息，则收集
                currentDeviceInfo ??= CollectDeviceInformation();
                
                result.CurrentFingerprint = currentDeviceInfo.DeviceFingerprint;
                result.IsMatch = string.Equals(storedFingerprint, currentDeviceInfo.DeviceFingerprint, StringComparison.OrdinalIgnoreCase);
                
                // 计算相似度
                result.SimilarityScore = CalculateSimilarity(storedFingerprint, currentDeviceInfo.DeviceFingerprint);
                
                // 分析差异
                result.DifferenceAnalysis = AnalyzeDifferences(currentDeviceInfo);
                
                result.Success = true;
            }
            catch (Exception ex)
            {
                result.Success = false;
                result.ErrorMessage = ex.Message;
            }

            return result;
        }

        /// <summary>
        /// 评估设备信任级别
        /// </summary>
        /// <param name="deviceInfo">设备信息</param>
        /// <returns>信任级别</returns>
        private DeviceTrustLevel EvaluateTrustLevel(DeviceInformation deviceInfo)
        {
            var score = 0;
            var maxScore = 0;

            // CPU信息
            maxScore += 2;
            if (deviceInfo.CpuInfo != null && !string.IsNullOrEmpty(deviceInfo.CpuInfo.ProcessorId))
                score += 2;
            else if (deviceInfo.CpuInfo != null && !string.IsNullOrEmpty(deviceInfo.CpuInfo.Name))
                score += 1;

            // 主板信息
            maxScore += 2;
            if (deviceInfo.MotherboardInfo != null && !string.IsNullOrEmpty(deviceInfo.MotherboardInfo.SerialNumber))
                score += 2;
            else if (deviceInfo.MotherboardInfo != null && !string.IsNullOrEmpty(deviceInfo.MotherboardInfo.Manufacturer))
                score += 1;

            // 硬盘信息
            maxScore += 2;
            if (deviceInfo.DiskInfoList?.Any(d => !string.IsNullOrEmpty(d.SerialNumber)) == true)
                score += 2;
            else if (deviceInfo.DiskInfoList?.Any() == true)
                score += 1;

            // 网络信息
            maxScore += 1;
            if (deviceInfo.NetworkAdapterInfoList?.Any(n => !string.IsNullOrEmpty(n.MacAddress) && n.MacAddress != "000000000000") == true)
                score += 1;

            // BIOS信息
            maxScore += 1;
            if (deviceInfo.BiosInfo != null && !string.IsNullOrEmpty(deviceInfo.BiosInfo.SerialNumber))
                score += 1;

            // 虚拟机检测
            if (deviceInfo.IsVirtualMachine)
                score = Math.Max(0, score - 2); // 虚拟机降低信任度

            // 计算百分比
            var percentage = maxScore > 0 ? (double)score / maxScore : 0;

            return percentage switch
            {
                >= 0.9 => DeviceTrustLevel.High,
                >= 0.7 => DeviceTrustLevel.Medium,
                >= 0.4 => DeviceTrustLevel.Low,
                _ => DeviceTrustLevel.Unknown
            };
        }

        /// <summary>
        /// 计算指纹相似度
        /// </summary>
        /// <param name="fingerprint1">指纹1</param>
        /// <param name="fingerprint2">指纹2</param>
        /// <returns>相似度百分比</returns>
        private static double CalculateSimilarity(string fingerprint1, string fingerprint2)
        {
            if (string.IsNullOrEmpty(fingerprint1) || string.IsNullOrEmpty(fingerprint2))
                return 0;

            if (fingerprint1.Equals(fingerprint2, StringComparison.OrdinalIgnoreCase))
                return 100;

            // 简单的字符相似度计算
            var commonChars = fingerprint1.Where((c, i) => i < fingerprint2.Length && fingerprint2[i] == c).Count();
            var maxLength = Math.Max(fingerprint1.Length, fingerprint2.Length);
            
            return maxLength > 0 ? (double)commonChars / maxLength * 100 : 0;
        }

        /// <summary>
        /// 分析设备差异
        /// </summary>
        /// <param name="deviceInfo">设备信息</param>
        /// <returns>差异分析</returns>
        private static string AnalyzeDifferences(DeviceInformation deviceInfo)
        {
            var analysis = new List<string>();

            if (deviceInfo.IsVirtualMachine)
                analysis.Add("设备运行在虚拟机环境中");

            if (deviceInfo.CpuInfo?.ErrorMessage != null)
                analysis.Add($"CPU信息收集失败: {deviceInfo.CpuInfo.ErrorMessage}");

            if (deviceInfo.MotherboardInfo?.ErrorMessage != null)
                analysis.Add($"主板信息收集失败: {deviceInfo.MotherboardInfo.ErrorMessage}");

            if (deviceInfo.DiskInfoList?.Any(d => !string.IsNullOrEmpty(d.ErrorMessage)) == true)
                analysis.Add("部分硬盘信息收集失败");

            if (deviceInfo.NetworkAdapterInfoList?.Any(n => !string.IsNullOrEmpty(n.ErrorMessage)) == true)
                analysis.Add("部分网络适配器信息收集失败");

            return analysis.Any() ? string.Join("; ", analysis) : "无明显异常";
        }

        /// <summary>
        /// 生成哈希值
        /// </summary>
        /// <param name="input">输入字符串</param>
        /// <returns>SHA256哈希值</returns>
        private static string GenerateHash(string input)
        {
            using var sha256 = SHA256.Create();
            var hashBytes = sha256.ComputeHash(Encoding.UTF8.GetBytes(input));
            return Convert.ToHexString(hashBytes).ToLowerInvariant();
        }

        /// <summary>
        /// 导出设备信息为JSON
        /// </summary>
        /// <param name="deviceInfo">设备信息</param>
        /// <param name="includeRawData">是否包含原始数据</param>
        /// <returns>JSON字符串</returns>
        public static string ExportToJson(DeviceInformation deviceInfo, bool includeRawData = false)
        {
            var options = new JsonSerializerOptions
            {
                WriteIndented = true,
                PropertyNamingPolicy = JsonNamingPolicy.CamelCase
            };

            if (!includeRawData)
            {
                // 创建简化版本，只包含关键信息
                var simplified = new
                {
                    deviceInfo.DeviceFingerprint,
                    deviceInfo.CollectedAt,
                    deviceInfo.TrustLevel,
                    deviceInfo.IsVirtualMachine,
                    CpuId = deviceInfo.CpuInfo?.ProcessorId,
                    MotherboardSerial = deviceInfo.MotherboardInfo?.SerialNumber,
                    PrimaryDiskSerial = deviceInfo.DiskInfoList?.FirstOrDefault(d => !string.IsNullOrEmpty(d.SerialNumber))?.SerialNumber,
                    PrimaryMacAddress = deviceInfo.NetworkAdapterInfoList?.FirstOrDefault(n => !string.IsNullOrEmpty(n.MacAddress))?.MacAddress,
                    SystemUuid = deviceInfo.SystemUuid
                };
                
                return JsonSerializer.Serialize(simplified, options);
            }

            return JsonSerializer.Serialize(deviceInfo, options);
        }

        /// <summary>
        /// 从JSON导入设备信息
        /// </summary>
        /// <param name="json">JSON字符串</param>
        /// <returns>设备信息</returns>
        public static DeviceInformation? ImportFromJson(string json)
        {
            try
            {
                var options = new JsonSerializerOptions
                {
                    PropertyNamingPolicy = JsonNamingPolicy.CamelCase
                };
                
                return JsonSerializer.Deserialize<DeviceInformation>(json, options);
            }
            catch
            {
                return null;
            }
        }
    }

    /// <summary>
    /// 设备指纹收集选项
    /// </summary>
    public class DeviceFingerprintOptions
    {
        /// <summary>
        /// 指纹版本
        /// </summary>
        public int FingerprintVersion { get; set; } = 1;

        /// <summary>
        /// 是否包含CPU信息
        /// </summary>
        public bool IncludeCpuInfo { get; set; } = true;

        /// <summary>
        /// 是否包含主板信息
        /// </summary>
        public bool IncludeMotherboardInfo { get; set; } = true;

        /// <summary>
        /// 是否包含内存信息
        /// </summary>
        public bool IncludeMemoryInfo { get; set; } = true;

        /// <summary>
        /// 是否包含硬盘信息
        /// </summary>
        public bool IncludeDiskInfo { get; set; } = true;

        /// <summary>
        /// 是否包含网络信息
        /// </summary>
        public bool IncludeNetworkInfo { get; set; } = true;

        /// <summary>
        /// 是否包含BIOS信息
        /// </summary>
        public bool IncludeBiosInfo { get; set; } = true;

        /// <summary>
        /// 是否包含操作系统信息
        /// </summary>
        public bool IncludeOsInfo { get; set; } = true;

        /// <summary>
        /// 是否包含系统UUID
        /// </summary>
        public bool IncludeSystemUuid { get; set; } = true;

        /// <summary>
        /// 最小指纹组件数量
        /// </summary>
        public int MinimumFingerprintComponents { get; set; } = 2;
    }

    /// <summary>
    /// 设备信息
    /// </summary>
    public class DeviceInformation
    {
        public string DeviceFingerprint { get; set; } = string.Empty;
        public DateTime CollectedAt { get; set; }
        public int FingerprintVersion { get; set; }
        public bool IsVirtualMachine { get; set; }
        public DeviceTrustLevel TrustLevel { get; set; }
        public string SystemUuid { get; set; } = string.Empty;
        public string ErrorMessage { get; set; } = string.Empty;

        public CpuInfo? CpuInfo { get; set; }
        public MotherboardInfo? MotherboardInfo { get; set; }
        public MemoryInfo? MemoryInfo { get; set; }
        public List<DiskInfo>? DiskInfoList { get; set; }
        public List<NetworkAdapterInfo>? NetworkAdapterInfoList { get; set; }
        public BiosInfo? BiosInfo { get; set; }
        public OperatingSystemInfo? OperatingSystemInfo { get; set; }
    }

    /// <summary>
    /// 指纹验证结果
    /// </summary>
    public class FingerprintValidationResult
    {
        public bool Success { get; set; }
        public bool IsMatch { get; set; }
        public string StoredFingerprint { get; set; } = string.Empty;
        public string CurrentFingerprint { get; set; } = string.Empty;
        public double SimilarityScore { get; set; }
        public string DifferenceAnalysis { get; set; } = string.Empty;
        public DateTime ValidationTime { get; set; }
        public string ErrorMessage { get; set; } = string.Empty;
    }

    /// <summary>
    /// 设备信任级别
    /// </summary>
    public enum DeviceTrustLevel
    {
        Unknown = 0,
        Low = 1,
        Medium = 2,
        High = 3,
        Verified = 4
    }
}
