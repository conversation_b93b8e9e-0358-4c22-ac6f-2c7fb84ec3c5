using System;
using System.Security.Cryptography;
using System.Text;
using System.Text.Json;

namespace LicenseGenerator.Crypto
{
    /// <summary>
    /// 数字签名服务 - 提供高级数字签名功能
    /// </summary>
    public class DigitalSignature : IDisposable
    {
        private RSAProvider? _rsaProvider;
        private bool _disposed = false;

        /// <summary>
        /// 使用RSA提供者初始化数字签名服务
        /// </summary>
        /// <param name="rsaProvider">RSA提供者</param>
        public DigitalSignature(RSAProvider rsaProvider)
        {
            _rsaProvider = rsaProvider ?? throw new ArgumentNullException(nameof(rsaProvider));
        }

        /// <summary>
        /// 使用私钥PEM初始化数字签名服务
        /// </summary>
        /// <param name="privateKeyPem">私钥PEM字符串</param>
        public DigitalSignature(string privateKeyPem)
        {
            _rsaProvider = new RSAProvider(privateKeyPem);
        }

        /// <summary>
        /// 创建对象的数字签名
        /// </summary>
        /// <typeparam name="T">对象类型</typeparam>
        /// <param name="obj">要签名的对象</param>
        /// <param name="hashAlgorithm">哈希算法</param>
        /// <returns>签名结果</returns>
        public SignatureResult SignObject<T>(T obj, HashAlgorithmName hashAlgorithm = default) where T : class
        {
            ThrowIfDisposed();
            
            hashAlgorithm = hashAlgorithm == default ? HashAlgorithmName.SHA256 : hashAlgorithm;
            
            try
            {
                // 序列化对象为JSON
                var jsonOptions = new JsonSerializerOptions
                {
                    WriteIndented = false,
                    PropertyNamingPolicy = JsonNamingPolicy.CamelCase
                };
                
                var jsonString = JsonSerializer.Serialize(obj, jsonOptions);
                var jsonBytes = Encoding.UTF8.GetBytes(jsonString);
                
                // 计算哈希值
                var hash = ComputeHash(jsonBytes, hashAlgorithm);
                
                // 创建签名
                var signature = _rsaProvider!.SignData(jsonBytes, hashAlgorithm);
                
                return new SignatureResult
                {
                    Success = true,
                    Signature = Convert.ToBase64String(signature),
                    Hash = Convert.ToBase64String(hash),
                    HashAlgorithm = hashAlgorithm.Name ?? "SHA256",
                    SignedData = jsonString,
                    SignedAt = DateTime.UtcNow,
                    PublicKeyFingerprint = _rsaProvider.GetPublicKeyFingerprint()
                };
            }
            catch (Exception ex)
            {
                return new SignatureResult
                {
                    Success = false,
                    ErrorMessage = ex.Message
                };
            }
        }

        /// <summary>
        /// 验证对象的数字签名
        /// </summary>
        /// <typeparam name="T">对象类型</typeparam>
        /// <param name="obj">要验证的对象</param>
        /// <param name="signatureBase64">Base64编码的签名</param>
        /// <param name="hashAlgorithm">哈希算法</param>
        /// <returns>验证结果</returns>
        public VerificationResult VerifyObject<T>(T obj, string signatureBase64, HashAlgorithmName hashAlgorithm = default) where T : class
        {
            ThrowIfDisposed();
            
            hashAlgorithm = hashAlgorithm == default ? HashAlgorithmName.SHA256 : hashAlgorithm;
            
            try
            {
                // 序列化对象为JSON
                var jsonOptions = new JsonSerializerOptions
                {
                    WriteIndented = false,
                    PropertyNamingPolicy = JsonNamingPolicy.CamelCase
                };
                
                var jsonString = JsonSerializer.Serialize(obj, jsonOptions);
                var jsonBytes = Encoding.UTF8.GetBytes(jsonString);
                var signature = Convert.FromBase64String(signatureBase64);
                
                // 验证签名
                var isValid = _rsaProvider!.VerifyData(jsonBytes, signature, hashAlgorithm);
                
                // 计算哈希值用于验证
                var hash = ComputeHash(jsonBytes, hashAlgorithm);
                
                return new VerificationResult
                {
                    IsValid = isValid,
                    Hash = Convert.ToBase64String(hash),
                    HashAlgorithm = hashAlgorithm.Name ?? "SHA256",
                    VerifiedData = jsonString,
                    VerifiedAt = DateTime.UtcNow,
                    PublicKeyFingerprint = _rsaProvider.GetPublicKeyFingerprint()
                };
            }
            catch (Exception ex)
            {
                return new VerificationResult
                {
                    IsValid = false,
                    ErrorMessage = ex.Message
                };
            }
        }

        /// <summary>
        /// 创建带时间戳的签名
        /// </summary>
        /// <typeparam name="T">对象类型</typeparam>
        /// <param name="obj">要签名的对象</param>
        /// <param name="hashAlgorithm">哈希算法</param>
        /// <returns>带时间戳的签名结果</returns>
        public TimestampedSignatureResult SignObjectWithTimestamp<T>(T obj, HashAlgorithmName hashAlgorithm = default) where T : class
        {
            ThrowIfDisposed();
            
            var timestamp = DateTime.UtcNow;
            var timestampedObj = new
            {
                Data = obj,
                Timestamp = timestamp.ToString("O"), // ISO 8601格式
                Nonce = Guid.NewGuid().ToString()
            };
            
            var signatureResult = SignObject(timestampedObj, hashAlgorithm);
            
            return new TimestampedSignatureResult
            {
                Success = signatureResult.Success,
                Signature = signatureResult.Signature,
                Hash = signatureResult.Hash,
                HashAlgorithm = signatureResult.HashAlgorithm,
                SignedData = signatureResult.SignedData,
                SignedAt = signatureResult.SignedAt,
                PublicKeyFingerprint = signatureResult.PublicKeyFingerprint,
                Timestamp = timestamp,
                ErrorMessage = signatureResult.ErrorMessage
            };
        }

        /// <summary>
        /// 验证带时间戳的签名
        /// </summary>
        /// <param name="signedData">签名的数据</param>
        /// <param name="signatureBase64">Base64编码的签名</param>
        /// <param name="maxAge">最大允许的时间差</param>
        /// <param name="hashAlgorithm">哈希算法</param>
        /// <returns>验证结果</returns>
        public TimestampedVerificationResult VerifyTimestampedSignature(string signedData, string signatureBase64, TimeSpan? maxAge = null, HashAlgorithmName hashAlgorithm = default)
        {
            ThrowIfDisposed();
            
            try
            {
                // 解析签名数据以提取时间戳
                var timestampedData = JsonSerializer.Deserialize<JsonElement>(signedData);
                
                if (!timestampedData.TryGetProperty("Timestamp", out var timestampElement))
                {
                    return new TimestampedVerificationResult
                    {
                        IsValid = false,
                        ErrorMessage = "Timestamp not found in signed data"
                    };
                }
                
                if (!DateTime.TryParse(timestampElement.GetString(), out var timestamp))
                {
                    return new TimestampedVerificationResult
                    {
                        IsValid = false,
                        ErrorMessage = "Invalid timestamp format"
                    };
                }
                
                // 检查时间戳是否在允许范围内
                if (maxAge.HasValue)
                {
                    var age = DateTime.UtcNow - timestamp;
                    if (age > maxAge.Value)
                    {
                        return new TimestampedVerificationResult
                        {
                            IsValid = false,
                            ErrorMessage = $"Signature is too old. Age: {age}, Max allowed: {maxAge.Value}",
                            Timestamp = timestamp
                        };
                    }
                }
                
                // 验证签名
                var jsonBytes = Encoding.UTF8.GetBytes(signedData);
                var signature = Convert.FromBase64String(signatureBase64);
                
                hashAlgorithm = hashAlgorithm == default ? HashAlgorithmName.SHA256 : hashAlgorithm;
                var isValid = _rsaProvider!.VerifyData(jsonBytes, signature, hashAlgorithm);
                
                var hash = ComputeHash(jsonBytes, hashAlgorithm);
                
                return new TimestampedVerificationResult
                {
                    IsValid = isValid,
                    Hash = Convert.ToBase64String(hash),
                    HashAlgorithm = hashAlgorithm.Name ?? "SHA256",
                    VerifiedData = signedData,
                    VerifiedAt = DateTime.UtcNow,
                    PublicKeyFingerprint = _rsaProvider.GetPublicKeyFingerprint(),
                    Timestamp = timestamp
                };
            }
            catch (Exception ex)
            {
                return new TimestampedVerificationResult
                {
                    IsValid = false,
                    ErrorMessage = ex.Message
                };
            }
        }

        /// <summary>
        /// 计算数据的哈希值
        /// </summary>
        /// <param name="data">数据</param>
        /// <param name="hashAlgorithm">哈希算法</param>
        /// <returns>哈希值</returns>
        private static byte[] ComputeHash(byte[] data, HashAlgorithmName hashAlgorithm)
        {
            HashAlgorithm hash = hashAlgorithm.Name switch
            {
                "SHA1" => SHA1.Create(),
                "SHA256" => SHA256.Create(),
                "SHA384" => SHA384.Create(),
                "SHA512" => SHA512.Create(),
                _ => SHA256.Create()
            };

            using (hash)
            {
                return hash.ComputeHash(data);
            }
        }

        private void ThrowIfDisposed()
        {
            if (_disposed)
                throw new ObjectDisposedException(nameof(DigitalSignature));
        }

        public void Dispose()
        {
            if (!_disposed)
            {
                _rsaProvider?.Dispose();
                _rsaProvider = null;
                _disposed = true;
            }
        }
    }

    /// <summary>
    /// 签名结果
    /// </summary>
    public class SignatureResult
    {
        /// <summary>
        /// 是否成功
        /// </summary>
        public bool Success { get; set; }

        /// <summary>
        /// 数字签名 (Base64编码)
        /// </summary>
        public string Signature { get; set; } = string.Empty;

        /// <summary>
        /// 数据哈希值 (Base64编码)
        /// </summary>
        public string Hash { get; set; } = string.Empty;

        /// <summary>
        /// 哈希算法名称
        /// </summary>
        public string HashAlgorithm { get; set; } = string.Empty;

        /// <summary>
        /// 签名的数据
        /// </summary>
        public string SignedData { get; set; } = string.Empty;

        /// <summary>
        /// 签名时间
        /// </summary>
        public DateTime SignedAt { get; set; }

        /// <summary>
        /// 公钥指纹
        /// </summary>
        public string PublicKeyFingerprint { get; set; } = string.Empty;

        /// <summary>
        /// 错误信息
        /// </summary>
        public string ErrorMessage { get; set; } = string.Empty;
    }

    /// <summary>
    /// 验证结果
    /// </summary>
    public class VerificationResult
    {
        /// <summary>
        /// 签名是否有效
        /// </summary>
        public bool IsValid { get; set; }

        /// <summary>
        /// 数据哈希值 (Base64编码)
        /// </summary>
        public string Hash { get; set; } = string.Empty;

        /// <summary>
        /// 哈希算法名称
        /// </summary>
        public string HashAlgorithm { get; set; } = string.Empty;

        /// <summary>
        /// 验证的数据
        /// </summary>
        public string VerifiedData { get; set; } = string.Empty;

        /// <summary>
        /// 验证时间
        /// </summary>
        public DateTime VerifiedAt { get; set; }

        /// <summary>
        /// 公钥指纹
        /// </summary>
        public string PublicKeyFingerprint { get; set; } = string.Empty;

        /// <summary>
        /// 错误信息
        /// </summary>
        public string ErrorMessage { get; set; } = string.Empty;
    }

    /// <summary>
    /// 带时间戳的签名结果
    /// </summary>
    public class TimestampedSignatureResult : SignatureResult
    {
        /// <summary>
        /// 时间戳
        /// </summary>
        public DateTime Timestamp { get; set; }
    }

    /// <summary>
    /// 带时间戳的验证结果
    /// </summary>
    public class TimestampedVerificationResult : VerificationResult
    {
        /// <summary>
        /// 时间戳
        /// </summary>
        public DateTime Timestamp { get; set; }
    }
}
