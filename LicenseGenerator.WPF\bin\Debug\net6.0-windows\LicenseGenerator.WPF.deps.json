{"runtimeTarget": {"name": ".NETCoreApp,Version=v6.0", "signature": ""}, "compilationOptions": {}, "targets": {".NETCoreApp,Version=v6.0": {"LicenseGenerator.WPF/1.0.0": {"dependencies": {"LicenseGenerator.Core": "1.0.0"}, "runtime": {"LicenseGenerator.WPF.dll": {}}}, "LicenseGenerator.Core/1.0.0": {"dependencies": {"LicenseGenerator.Crypto": "1.0.0", "LicenseGenerator.Data": "1.0.0", "LicenseGenerator.DeviceInfo": "1.0.0"}, "runtime": {"LicenseGenerator.Core.dll": {"assemblyVersion": "1.0.0.0", "fileVersion": "1.0.0.0"}}}, "LicenseGenerator.Crypto/1.0.0": {"runtime": {"LicenseGenerator.Crypto.dll": {"assemblyVersion": "1.0.0.0", "fileVersion": "1.0.0.0"}}}, "LicenseGenerator.Data/1.0.0": {"runtime": {"LicenseGenerator.Data.dll": {"assemblyVersion": "1.0.0.0", "fileVersion": "1.0.0.0"}}}, "LicenseGenerator.DeviceInfo/1.0.0": {"runtime": {"LicenseGenerator.DeviceInfo.dll": {"assemblyVersion": "1.0.0.0", "fileVersion": "1.0.0.0"}}}}}, "libraries": {"LicenseGenerator.WPF/1.0.0": {"type": "project", "serviceable": false, "sha512": ""}, "LicenseGenerator.Core/1.0.0": {"type": "project", "serviceable": false, "sha512": ""}, "LicenseGenerator.Crypto/1.0.0": {"type": "project", "serviceable": false, "sha512": ""}, "LicenseGenerator.Data/1.0.0": {"type": "project", "serviceable": false, "sha512": ""}, "LicenseGenerator.DeviceInfo/1.0.0": {"type": "project", "serviceable": false, "sha512": ""}}}