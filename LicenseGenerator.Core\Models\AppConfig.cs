using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;

namespace LicenseGenerator.Core.Models
{
    /// <summary>
    /// 应用程序配置模型
    /// </summary>
    public class AppConfig
    {
        /// <summary>
        /// 配置唯一标识符
        /// </summary>
        public Guid Id { get; set; } = Guid.NewGuid();

        /// <summary>
        /// 应用程序标识符
        /// </summary>
        [Required]
        [StringLength(100)]
        public string AppId { get; set; } = string.Empty;

        /// <summary>
        /// 应用程序名称
        /// </summary>
        [Required]
        [StringLength(200)]
        public string AppName { get; set; } = string.Empty;

        /// <summary>
        /// 应用程序版本
        /// </summary>
        [StringLength(50)]
        public string Version { get; set; } = "1.0.0";

        /// <summary>
        /// 应用程序描述
        /// </summary>
        [StringLength(1000)]
        public string Description { get; set; } = string.Empty;

        /// <summary>
        /// 可用功能列表
        /// </summary>
        public List<FeatureInfo> AvailableFeatures { get; set; } = new List<FeatureInfo>();

        /// <summary>
        /// 默认License类型
        /// </summary>
        public LicenseType DefaultLicenseType { get; set; } = LicenseType.Trial;

        /// <summary>
        /// 最大有效期天数 (0表示无限制)
        /// </summary>
        public int MaxValidityDays { get; set; } = 365;

        /// <summary>
        /// 默认有效期天数
        /// </summary>
        public int DefaultValidityDays { get; set; } = 30;

        /// <summary>
        /// 是否允许离线验证
        /// </summary>
        public bool AllowOfflineValidation { get; set; } = true;

        /// <summary>
        /// 是否需要设备绑定
        /// </summary>
        public bool RequireDeviceBinding { get; set; } = true;

        /// <summary>
        /// 最大设备绑定数量
        /// </summary>
        public int MaxDeviceBindings { get; set; } = 1;

        /// <summary>
        /// License模板配置
        /// </summary>
        public List<LicenseTemplate> LicenseTemplates { get; set; } = new List<LicenseTemplate>();

        /// <summary>
        /// 创建时间
        /// </summary>
        public DateTime CreatedAt { get; set; } = DateTime.UtcNow;

        /// <summary>
        /// 最后更新时间
        /// </summary>
        public DateTime UpdatedAt { get; set; } = DateTime.UtcNow;

        /// <summary>
        /// 是否激活
        /// </summary>
        public bool IsActive { get; set; } = true;

        /// <summary>
        /// 备注信息
        /// </summary>
        [StringLength(1000)]
        public string Notes { get; set; } = string.Empty;
    }

    /// <summary>
    /// 功能信息模型
    /// </summary>
    public class FeatureInfo
    {
        /// <summary>
        /// 功能标识符
        /// </summary>
        [Required]
        [StringLength(100)]
        public string FeatureId { get; set; } = string.Empty;

        /// <summary>
        /// 功能名称
        /// </summary>
        [Required]
        [StringLength(200)]
        public string Name { get; set; } = string.Empty;

        /// <summary>
        /// 功能描述
        /// </summary>
        [StringLength(500)]
        public string Description { get; set; } = string.Empty;

        /// <summary>
        /// 功能分类
        /// </summary>
        [StringLength(100)]
        public string Category { get; set; } = string.Empty;

        /// <summary>
        /// 是否为核心功能
        /// </summary>
        public bool IsCore { get; set; } = false;

        /// <summary>
        /// 功能优先级 (数值越小优先级越高)
        /// </summary>
        public int Priority { get; set; } = 100;

        /// <summary>
        /// 是否默认启用
        /// </summary>
        public bool IsEnabledByDefault { get; set; } = true;

        /// <summary>
        /// 依赖的其他功能
        /// </summary>
        public List<string> Dependencies { get; set; } = new List<string>();
    }

    /// <summary>
    /// License模板模型
    /// </summary>
    public class LicenseTemplate
    {
        /// <summary>
        /// 模板唯一标识符
        /// </summary>
        public Guid Id { get; set; } = Guid.NewGuid();

        /// <summary>
        /// 模板名称
        /// </summary>
        [Required]
        [StringLength(100)]
        public string Name { get; set; } = string.Empty;

        /// <summary>
        /// 模板描述
        /// </summary>
        [StringLength(500)]
        public string Description { get; set; } = string.Empty;

        /// <summary>
        /// License类型
        /// </summary>
        public LicenseType LicenseType { get; set; } = LicenseType.Trial;

        /// <summary>
        /// 有效期天数 (null表示永不过期)
        /// </summary>
        public int? ValidityDays { get; set; }

        /// <summary>
        /// 最大并发用户数
        /// </summary>
        public int MaxConcurrentUsers { get; set; } = 1;

        /// <summary>
        /// 包含的功能列表
        /// </summary>
        public List<string> IncludedFeatures { get; set; } = new List<string>();

        /// <summary>
        /// 是否为默认模板
        /// </summary>
        public bool IsDefault { get; set; } = false;

        /// <summary>
        /// 创建时间
        /// </summary>
        public DateTime CreatedAt { get; set; } = DateTime.UtcNow;

        /// <summary>
        /// 是否激活
        /// </summary>
        public bool IsActive { get; set; } = true;
    }
}
