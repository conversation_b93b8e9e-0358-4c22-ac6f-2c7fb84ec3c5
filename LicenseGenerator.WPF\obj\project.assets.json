{"version": 3, "targets": {"net6.0-windows7.0": {"LicenseGenerator.Core/1.0.0": {"type": "project", "framework": ".NETCoreApp,Version=v6.0", "dependencies": {"LicenseGenerator.Crypto": "1.0.0", "LicenseGenerator.Data": "1.0.0", "LicenseGenerator.DeviceInfo": "1.0.0"}, "compile": {"bin/placeholder/LicenseGenerator.Core.dll": {}}, "runtime": {"bin/placeholder/LicenseGenerator.Core.dll": {}}}, "LicenseGenerator.Crypto/1.0.0": {"type": "project", "framework": ".NETCoreApp,Version=v6.0", "compile": {"bin/placeholder/LicenseGenerator.Crypto.dll": {}}, "runtime": {"bin/placeholder/LicenseGenerator.Crypto.dll": {}}}, "LicenseGenerator.Data/1.0.0": {"type": "project", "framework": ".NETCoreApp,Version=v6.0", "compile": {"bin/placeholder/LicenseGenerator.Data.dll": {}}, "runtime": {"bin/placeholder/LicenseGenerator.Data.dll": {}}}, "LicenseGenerator.DeviceInfo/1.0.0": {"type": "project", "framework": ".NETCoreApp,Version=v6.0", "compile": {"bin/placeholder/LicenseGenerator.DeviceInfo.dll": {}}, "runtime": {"bin/placeholder/LicenseGenerator.DeviceInfo.dll": {}}}}}, "libraries": {"LicenseGenerator.Core/1.0.0": {"type": "project", "path": "../LicenseGenerator.Core/LicenseGenerator.Core.csproj", "msbuildProject": "../LicenseGenerator.Core/LicenseGenerator.Core.csproj"}, "LicenseGenerator.Crypto/1.0.0": {"type": "project", "path": "../LicenseGenerator.Crypto/LicenseGenerator.Crypto.csproj", "msbuildProject": "../LicenseGenerator.Crypto/LicenseGenerator.Crypto.csproj"}, "LicenseGenerator.Data/1.0.0": {"type": "project", "path": "../LicenseGenerator.Data/LicenseGenerator.Data.csproj", "msbuildProject": "../LicenseGenerator.Data/LicenseGenerator.Data.csproj"}, "LicenseGenerator.DeviceInfo/1.0.0": {"type": "project", "path": "../LicenseGenerator.DeviceInfo/LicenseGenerator.DeviceInfo.csproj", "msbuildProject": "../LicenseGenerator.DeviceInfo/LicenseGenerator.DeviceInfo.csproj"}}, "projectFileDependencyGroups": {"net6.0-windows7.0": ["LicenseGenerator.Core >= 1.0.0"]}, "packageFolders": {"C:\\Users\\<USER>\\.nuget\\packages\\": {}, "E:\\DevExpress 23.2\\Components\\Offline Packages": {}, "e:\\DevExpress 24.2\\Components\\Offline Packages": {}, "E:\\Microsoft Visual Studio\\Shared\\NuGetPackages": {}, "D:\\Syncfusion\\Essential Studio\\WPF\\29.1.33\\ToolboxNuGetPackages": {}}, "project": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\00 Crack\\LicenseGenerator.WPF\\LicenseGenerator.WPF.csproj", "projectName": "LicenseGenerator.WPF", "projectPath": "D:\\00 Crack\\LicenseGenerator.WPF\\LicenseGenerator.WPF.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\00 Crack\\LicenseGenerator.WPF\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["E:\\DevExpress 23.2\\Components\\Offline Packages", "e:\\DevExpress 24.2\\Components\\Offline Packages", "E:\\Microsoft Visual Studio\\Shared\\NuGetPackages", "D:\\Syncfusion\\Essential Studio\\WPF\\29.1.33\\ToolboxNuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\DevExpress 23.2.config", "C:\\Program Files (x86)\\NuGet\\Config\\DevExpress 24.2.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config", "C:\\Program Files (x86)\\NuGet\\Config\\Syncfusion Toolbox for WPF.config"], "originalTargetFrameworks": ["net6.0-windows"], "sources": {"C:\\Program Files\\dotnet\\library-packs": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net6.0-windows7.0": {"targetAlias": "net6.0-windows", "projectReferences": {"D:\\00 Crack\\LicenseGenerator.Core\\LicenseGenerator.Core.csproj": {"projectPath": "D:\\00 Crack\\LicenseGenerator.Core\\LicenseGenerator.Core.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net6.0-windows7.0": {"targetAlias": "net6.0-windows", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}, "Microsoft.WindowsDesktop.App.WPF": {"privateAssets": "none"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.300\\RuntimeIdentifierGraph.json"}}}}