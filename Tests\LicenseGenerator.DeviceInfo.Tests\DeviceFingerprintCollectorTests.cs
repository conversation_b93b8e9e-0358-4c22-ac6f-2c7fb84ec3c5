using System;
using System.Linq;
using LicenseGenerator.DeviceInfo;
using Xunit;

namespace LicenseGenerator.DeviceInfo.Tests
{
    /// <summary>
    /// 设备指纹收集器测试
    /// </summary>
    public class DeviceFingerprintCollectorTests
    {
        [Fact]
        public void CollectDeviceInformation_ShouldReturnValidInformation()
        {
            // Arrange
            var collector = new DeviceFingerprintCollector();

            // Act
            var deviceInfo = collector.CollectDeviceInformation();

            // Assert
            Assert.NotNull(deviceInfo);
            Assert.NotEmpty(deviceInfo.DeviceFingerprint);
            Assert.True(deviceInfo.CollectedAt <= DateTime.UtcNow);
            Assert.Equal(1, deviceInfo.FingerprintVersion);
            Assert.True(Enum.IsDefined(typeof(DeviceTrustLevel), deviceInfo.TrustLevel));
        }

        [Fact]
        public void CollectDeviceInformation_WithCustomOptions_ShouldRespectOptions()
        {
            // Arrange
            var options = new DeviceFingerprintOptions
            {
                IncludeCpuInfo = false,
                IncludeMemoryInfo = false,
                IncludeBiosInfo = false,
                FingerprintVersion = 2
            };
            var collector = new DeviceFingerprintCollector(options);

            // Act
            var deviceInfo = collector.CollectDeviceInformation();

            // Assert
            Assert.NotNull(deviceInfo);
            Assert.Equal(2, deviceInfo.FingerprintVersion);
            Assert.Null(deviceInfo.CpuInfo);
            Assert.Null(deviceInfo.MemoryInfo);
            Assert.Null(deviceInfo.BiosInfo);
        }

        [Fact]
        public void GenerateDeviceFingerprint_ShouldReturnConsistentFingerprint()
        {
            // Arrange
            var collector = new DeviceFingerprintCollector();
            var deviceInfo = collector.CollectDeviceInformation();

            // Act
            var fingerprint1 = collector.GenerateDeviceFingerprint(deviceInfo);
            var fingerprint2 = collector.GenerateDeviceFingerprint(deviceInfo);

            // Assert
            Assert.NotEmpty(fingerprint1);
            Assert.Equal(fingerprint1, fingerprint2);
            Assert.Equal(64, fingerprint1.Length); // SHA256 hex string length
        }

        [Fact]
        public void GenerateDeviceFingerprintFromString_ShouldReturnValidHash()
        {
            // Arrange
            var testString = "CPU:TestCPU|MB:TestMotherboard|DISK:TestDisk";

            // Act
            var fingerprint = DeviceFingerprintCollector.GenerateDeviceFingerprintFromString(testString);

            // Assert
            Assert.NotEmpty(fingerprint);
            Assert.Equal(64, fingerprint.Length); // SHA256 hex string length
            Assert.True(fingerprint.All(c => char.IsDigit(c) || (c >= 'a' && c <= 'f')));
        }

        [Fact]
        public void ValidateFingerprint_WithMatchingFingerprint_ShouldReturnTrue()
        {
            // Arrange
            var collector = new DeviceFingerprintCollector();
            var deviceInfo = collector.CollectDeviceInformation();
            var storedFingerprint = deviceInfo.DeviceFingerprint;

            // Act
            var result = collector.ValidateFingerprint(storedFingerprint, deviceInfo);

            // Assert
            Assert.True(result.Success);
            Assert.True(result.IsMatch);
            Assert.Equal(storedFingerprint, result.StoredFingerprint);
            Assert.Equal(deviceInfo.DeviceFingerprint, result.CurrentFingerprint);
            Assert.Equal(100.0, result.SimilarityScore);
        }

        [Fact]
        public void ValidateFingerprint_WithDifferentFingerprint_ShouldReturnFalse()
        {
            // Arrange
            var collector = new DeviceFingerprintCollector();
            var deviceInfo = collector.CollectDeviceInformation();
            var differentFingerprint = "different_fingerprint_hash_value_here_1234567890abcdef";

            // Act
            var result = collector.ValidateFingerprint(differentFingerprint, deviceInfo);

            // Assert
            Assert.True(result.Success);
            Assert.False(result.IsMatch);
            Assert.Equal(differentFingerprint, result.StoredFingerprint);
            Assert.Equal(deviceInfo.DeviceFingerprint, result.CurrentFingerprint);
            Assert.True(result.SimilarityScore < 100.0);
        }

        [Fact]
        public void ExportToJson_ShouldReturnValidJson()
        {
            // Arrange
            var collector = new DeviceFingerprintCollector();
            var deviceInfo = collector.CollectDeviceInformation();

            // Act
            var json = DeviceFingerprintCollector.ExportToJson(deviceInfo, false);

            // Assert
            Assert.NotEmpty(json);
            Assert.Contains("deviceFingerprint", json);
            Assert.Contains("collectedAt", json);
            Assert.Contains("trustLevel", json);
        }

        [Fact]
        public void ExportToJson_WithRawData_ShouldIncludeAllData()
        {
            // Arrange
            var collector = new DeviceFingerprintCollector();
            var deviceInfo = collector.CollectDeviceInformation();

            // Act
            var json = DeviceFingerprintCollector.ExportToJson(deviceInfo, true);

            // Assert
            Assert.NotEmpty(json);
            Assert.Contains("deviceFingerprint", json);
            Assert.Contains("cpuInfo", json);
            Assert.Contains("motherboardInfo", json);
        }

        [Fact]
        public void ImportFromJson_ShouldRestoreDeviceInformation()
        {
            // Arrange
            var collector = new DeviceFingerprintCollector();
            var originalDeviceInfo = collector.CollectDeviceInformation();
            var json = DeviceFingerprintCollector.ExportToJson(originalDeviceInfo, true);

            // Act
            var restoredDeviceInfo = DeviceFingerprintCollector.ImportFromJson(json);

            // Assert
            Assert.NotNull(restoredDeviceInfo);
            Assert.Equal(originalDeviceInfo.DeviceFingerprint, restoredDeviceInfo.DeviceFingerprint);
            Assert.Equal(originalDeviceInfo.FingerprintVersion, restoredDeviceInfo.FingerprintVersion);
            Assert.Equal(originalDeviceInfo.IsVirtualMachine, restoredDeviceInfo.IsVirtualMachine);
        }

        [Fact]
        public void ImportFromJson_WithInvalidJson_ShouldReturnNull()
        {
            // Arrange
            var invalidJson = "{ invalid json content }";

            // Act
            var result = DeviceFingerprintCollector.ImportFromJson(invalidJson);

            // Assert
            Assert.Null(result);
        }

        [Theory]
        [InlineData(true, true, true, true)]
        [InlineData(false, false, false, false)]
        [InlineData(true, false, true, false)]
        public void CollectDeviceInformation_WithVariousOptions_ShouldWork(bool includeCpu, bool includeMemory, bool includeBios, bool includeNetwork)
        {
            // Arrange
            var options = new DeviceFingerprintOptions
            {
                IncludeCpuInfo = includeCpu,
                IncludeMemoryInfo = includeMemory,
                IncludeBiosInfo = includeBios,
                IncludeNetworkInfo = includeNetwork
            };
            var collector = new DeviceFingerprintCollector(options);

            // Act
            var deviceInfo = collector.CollectDeviceInformation();

            // Assert
            Assert.NotNull(deviceInfo);
            Assert.NotEmpty(deviceInfo.DeviceFingerprint);
            
            if (includeCpu)
                Assert.NotNull(deviceInfo.CpuInfo);
            else
                Assert.Null(deviceInfo.CpuInfo);

            if (includeMemory)
                Assert.NotNull(deviceInfo.MemoryInfo);
            else
                Assert.Null(deviceInfo.MemoryInfo);

            if (includeBios)
                Assert.NotNull(deviceInfo.BiosInfo);
            else
                Assert.Null(deviceInfo.BiosInfo);

            if (includeNetwork)
                Assert.NotNull(deviceInfo.NetworkAdapterInfoList);
            else
                Assert.Null(deviceInfo.NetworkAdapterInfoList);
        }

        [Fact]
        public void DeviceFingerprintOptions_DefaultValues_ShouldBeCorrect()
        {
            // Arrange & Act
            var options = new DeviceFingerprintOptions();

            // Assert
            Assert.Equal(1, options.FingerprintVersion);
            Assert.True(options.IncludeCpuInfo);
            Assert.True(options.IncludeMotherboardInfo);
            Assert.True(options.IncludeMemoryInfo);
            Assert.True(options.IncludeDiskInfo);
            Assert.True(options.IncludeNetworkInfo);
            Assert.True(options.IncludeBiosInfo);
            Assert.True(options.IncludeOsInfo);
            Assert.True(options.IncludeSystemUuid);
            Assert.Equal(2, options.MinimumFingerprintComponents);
        }

        [Fact]
        public void FingerprintValidationResult_Properties_ShouldBeInitialized()
        {
            // Arrange & Act
            var result = new FingerprintValidationResult();

            // Assert
            Assert.False(result.Success);
            Assert.False(result.IsMatch);
            Assert.Empty(result.StoredFingerprint);
            Assert.Empty(result.CurrentFingerprint);
            Assert.Equal(0, result.SimilarityScore);
            Assert.Empty(result.DifferenceAnalysis);
            Assert.Empty(result.ErrorMessage);
        }

        [Fact]
        public void DeviceInformation_Properties_ShouldBeInitialized()
        {
            // Arrange & Act
            var deviceInfo = new DeviceInformation();

            // Assert
            Assert.Empty(deviceInfo.DeviceFingerprint);
            Assert.Equal(default(DateTime), deviceInfo.CollectedAt);
            Assert.Equal(0, deviceInfo.FingerprintVersion);
            Assert.False(deviceInfo.IsVirtualMachine);
            Assert.Equal(DeviceTrustLevel.Unknown, deviceInfo.TrustLevel);
            Assert.Empty(deviceInfo.SystemUuid);
            Assert.Empty(deviceInfo.ErrorMessage);
        }
    }
}
