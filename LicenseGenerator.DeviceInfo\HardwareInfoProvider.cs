using System;
using System.Collections.Generic;
using System.Linq;
using System.Management;
using System.Net.NetworkInformation;
using System.Runtime.InteropServices;
using System.Text;

namespace LicenseGenerator.DeviceInfo
{
    /// <summary>
    /// 硬件信息提供者 - 负责收集各种硬件信息
    /// </summary>
    public class HardwareInfoProvider
    {
        /// <summary>
        /// 获取CPU信息
        /// </summary>
        /// <returns>CPU信息</returns>
        public static CpuInfo GetCpuInfo()
        {
            var cpuInfo = new CpuInfo();
            
            try
            {
                if (RuntimeInformation.IsOSPlatform(OSPlatform.Windows))
                {
                    using var searcher = new ManagementObjectSearcher("SELECT * FROM Win32_Processor");
                    using var collection = searcher.Get();
                    
                    var processor = collection.Cast<ManagementObject>().FirstOrDefault();
                    if (processor != null)
                    {
                        cpuInfo.ProcessorId = processor["ProcessorId"]?.ToString() ?? string.Empty;
                        cpuInfo.Name = processor["Name"]?.ToString() ?? string.Empty;
                        cpuInfo.Manufacturer = processor["Manufacturer"]?.ToString() ?? string.Empty;
                        cpuInfo.Architecture = processor["Architecture"]?.ToString() ?? string.Empty;
                        cpuInfo.MaxClockSpeed = Convert.ToUInt32(processor["MaxClockSpeed"] ?? 0);
                        cpuInfo.NumberOfCores = Convert.ToUInt32(processor["NumberOfCores"] ?? 0);
                        cpuInfo.NumberOfLogicalProcessors = Convert.ToUInt32(processor["NumberOfLogicalProcessors"] ?? 0);
                    }
                }
                else
                {
                    // 对于非Windows平台，使用替代方法
                    cpuInfo.Name = Environment.GetEnvironmentVariable("PROCESSOR_IDENTIFIER") ?? "Unknown";
                    cpuInfo.NumberOfLogicalProcessors = (uint)Environment.ProcessorCount;
                }
            }
            catch (Exception ex)
            {
                cpuInfo.ErrorMessage = ex.Message;
            }
            
            return cpuInfo;
        }

        /// <summary>
        /// 获取主板信息
        /// </summary>
        /// <returns>主板信息</returns>
        public static MotherboardInfo GetMotherboardInfo()
        {
            var motherboardInfo = new MotherboardInfo();
            
            try
            {
                if (RuntimeInformation.IsOSPlatform(OSPlatform.Windows))
                {
                    using var searcher = new ManagementObjectSearcher("SELECT * FROM Win32_BaseBoard");
                    using var collection = searcher.Get();
                    
                    var baseBoard = collection.Cast<ManagementObject>().FirstOrDefault();
                    if (baseBoard != null)
                    {
                        motherboardInfo.SerialNumber = baseBoard["SerialNumber"]?.ToString() ?? string.Empty;
                        motherboardInfo.Manufacturer = baseBoard["Manufacturer"]?.ToString() ?? string.Empty;
                        motherboardInfo.Product = baseBoard["Product"]?.ToString() ?? string.Empty;
                        motherboardInfo.Version = baseBoard["Version"]?.ToString() ?? string.Empty;
                    }
                }
            }
            catch (Exception ex)
            {
                motherboardInfo.ErrorMessage = ex.Message;
            }
            
            return motherboardInfo;
        }

        /// <summary>
        /// 获取内存信息
        /// </summary>
        /// <returns>内存信息</returns>
        public static MemoryInfo GetMemoryInfo()
        {
            var memoryInfo = new MemoryInfo();
            
            try
            {
                if (RuntimeInformation.IsOSPlatform(OSPlatform.Windows))
                {
                    // 获取物理内存信息
                    using var searcher = new ManagementObjectSearcher("SELECT * FROM Win32_PhysicalMemory");
                    using var collection = searcher.Get();
                    
                    var memoryModules = collection.Cast<ManagementObject>().ToList();
                    memoryInfo.TotalMemoryBytes = memoryModules.Sum(m => Convert.ToInt64(m["Capacity"] ?? 0));
                    memoryInfo.MemorySlots = memoryModules.Count;
                    
                    foreach (var module in memoryModules)
                    {
                        var manufacturer = module["Manufacturer"]?.ToString();
                        if (!string.IsNullOrWhiteSpace(manufacturer) && !memoryInfo.Manufacturers.Contains(manufacturer))
                        {
                            memoryInfo.Manufacturers.Add(manufacturer);
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                memoryInfo.ErrorMessage = ex.Message;
            }
            
            return memoryInfo;
        }

        /// <summary>
        /// 获取硬盘信息
        /// </summary>
        /// <returns>硬盘信息列表</returns>
        public static List<DiskInfo> GetDiskInfo()
        {
            var diskInfoList = new List<DiskInfo>();
            
            try
            {
                if (RuntimeInformation.IsOSPlatform(OSPlatform.Windows))
                {
                    using var searcher = new ManagementObjectSearcher("SELECT * FROM Win32_DiskDrive");
                    using var collection = searcher.Get();
                    
                    foreach (ManagementObject disk in collection)
                    {
                        var diskInfo = new DiskInfo
                        {
                            SerialNumber = disk["SerialNumber"]?.ToString()?.Trim() ?? string.Empty,
                            Model = disk["Model"]?.ToString() ?? string.Empty,
                            Manufacturer = disk["Manufacturer"]?.ToString() ?? string.Empty,
                            Size = Convert.ToInt64(disk["Size"] ?? 0),
                            InterfaceType = disk["InterfaceType"]?.ToString() ?? string.Empty,
                            MediaType = disk["MediaType"]?.ToString() ?? string.Empty
                        };
                        
                        diskInfoList.Add(diskInfo);
                    }
                }
            }
            catch (Exception ex)
            {
                diskInfoList.Add(new DiskInfo { ErrorMessage = ex.Message });
            }
            
            return diskInfoList;
        }

        /// <summary>
        /// 获取网络适配器信息
        /// </summary>
        /// <returns>网络适配器信息列表</returns>
        public static List<NetworkAdapterInfo> GetNetworkAdapterInfo()
        {
            var adapterInfoList = new List<NetworkAdapterInfo>();
            
            try
            {
                var networkInterfaces = NetworkInterface.GetAllNetworkInterfaces();
                
                foreach (var networkInterface in networkInterfaces)
                {
                    // 跳过回环和隧道接口
                    if (networkInterface.NetworkInterfaceType == NetworkInterfaceType.Loopback ||
                        networkInterface.NetworkInterfaceType == NetworkInterfaceType.Tunnel)
                        continue;
                    
                    var adapterInfo = new NetworkAdapterInfo
                    {
                        Name = networkInterface.Name,
                        Description = networkInterface.Description,
                        MacAddress = networkInterface.GetPhysicalAddress().ToString(),
                        NetworkInterfaceType = networkInterface.NetworkInterfaceType.ToString(),
                        OperationalStatus = networkInterface.OperationalStatus.ToString(),
                        Speed = networkInterface.Speed,
                        IsReceiveOnly = networkInterface.IsReceiveOnly
                    };
                    
                    adapterInfoList.Add(adapterInfo);
                }
            }
            catch (Exception ex)
            {
                adapterInfoList.Add(new NetworkAdapterInfo { ErrorMessage = ex.Message });
            }
            
            return adapterInfoList;
        }

        /// <summary>
        /// 获取BIOS信息
        /// </summary>
        /// <returns>BIOS信息</returns>
        public static BiosInfo GetBiosInfo()
        {
            var biosInfo = new BiosInfo();
            
            try
            {
                if (RuntimeInformation.IsOSPlatform(OSPlatform.Windows))
                {
                    using var searcher = new ManagementObjectSearcher("SELECT * FROM Win32_BIOS");
                    using var collection = searcher.Get();
                    
                    var bios = collection.Cast<ManagementObject>().FirstOrDefault();
                    if (bios != null)
                    {
                        biosInfo.Manufacturer = bios["Manufacturer"]?.ToString() ?? string.Empty;
                        biosInfo.Version = bios["SMBIOSBIOSVersion"]?.ToString() ?? string.Empty;
                        biosInfo.SerialNumber = bios["SerialNumber"]?.ToString() ?? string.Empty;
                        
                        if (DateTime.TryParse(bios["ReleaseDate"]?.ToString(), out var releaseDate))
                        {
                            biosInfo.ReleaseDate = releaseDate;
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                biosInfo.ErrorMessage = ex.Message;
            }
            
            return biosInfo;
        }

        /// <summary>
        /// 获取操作系统信息
        /// </summary>
        /// <returns>操作系统信息</returns>
        public static OperatingSystemInfo GetOperatingSystemInfo()
        {
            var osInfo = new OperatingSystemInfo();
            
            try
            {
                osInfo.Name = Environment.OSVersion.Platform.ToString();
                osInfo.Version = Environment.OSVersion.Version.ToString();
                osInfo.Architecture = RuntimeInformation.OSArchitecture.ToString();
                
                if (RuntimeInformation.IsOSPlatform(OSPlatform.Windows))
                {
                    using var searcher = new ManagementObjectSearcher("SELECT * FROM Win32_OperatingSystem");
                    using var collection = searcher.Get();
                    
                    var os = collection.Cast<ManagementObject>().FirstOrDefault();
                    if (os != null)
                    {
                        osInfo.Name = os["Caption"]?.ToString() ?? osInfo.Name;
                        osInfo.Version = os["Version"]?.ToString() ?? osInfo.Version;
                        
                        if (DateTime.TryParse(os["InstallDate"]?.ToString(), out var installDate))
                        {
                            osInfo.InstallDate = installDate;
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                osInfo.ErrorMessage = ex.Message;
            }
            
            return osInfo;
        }

        /// <summary>
        /// 获取系统UUID (仅Windows)
        /// </summary>
        /// <returns>系统UUID</returns>
        public static string GetSystemUuid()
        {
            try
            {
                if (RuntimeInformation.IsOSPlatform(OSPlatform.Windows))
                {
                    using var searcher = new ManagementObjectSearcher("SELECT * FROM Win32_ComputerSystemProduct");
                    using var collection = searcher.Get();
                    
                    var product = collection.Cast<ManagementObject>().FirstOrDefault();
                    return product?["UUID"]?.ToString() ?? string.Empty;
                }
            }
            catch
            {
                // 忽略异常
            }
            
            return string.Empty;
        }

        /// <summary>
        /// 检查是否运行在虚拟机中
        /// </summary>
        /// <returns>是否为虚拟机</returns>
        public static bool IsVirtualMachine()
        {
            try
            {
                if (RuntimeInformation.IsOSPlatform(OSPlatform.Windows))
                {
                    // 检查常见的虚拟机制造商
                    var motherboard = GetMotherboardInfo();
                    var cpu = GetCpuInfo();
                    var bios = GetBiosInfo();
                    
                    var virtualIndicators = new[]
                    {
                        "VMware", "VirtualBox", "QEMU", "Xen", "Microsoft Corporation",
                        "Parallels", "Virtual", "VBOX", "VMWARE"
                    };
                    
                    var checkStrings = new[]
                    {
                        motherboard.Manufacturer,
                        motherboard.Product,
                        cpu.Manufacturer,
                        cpu.Name,
                        bios.Manufacturer
                    };
                    
                    return checkStrings.Any(str => 
                        virtualIndicators.Any(indicator => 
                            str?.Contains(indicator, StringComparison.OrdinalIgnoreCase) == true));
                }
            }
            catch
            {
                // 忽略异常
            }
            
            return false;
        }
    }

    /// <summary>
    /// CPU信息
    /// </summary>
    public class CpuInfo
    {
        public string ProcessorId { get; set; } = string.Empty;
        public string Name { get; set; } = string.Empty;
        public string Manufacturer { get; set; } = string.Empty;
        public string Architecture { get; set; } = string.Empty;
        public uint MaxClockSpeed { get; set; }
        public uint NumberOfCores { get; set; }
        public uint NumberOfLogicalProcessors { get; set; }
        public string ErrorMessage { get; set; } = string.Empty;
    }

    /// <summary>
    /// 主板信息
    /// </summary>
    public class MotherboardInfo
    {
        public string SerialNumber { get; set; } = string.Empty;
        public string Manufacturer { get; set; } = string.Empty;
        public string Product { get; set; } = string.Empty;
        public string Version { get; set; } = string.Empty;
        public string ErrorMessage { get; set; } = string.Empty;
    }

    /// <summary>
    /// 内存信息
    /// </summary>
    public class MemoryInfo
    {
        public long TotalMemoryBytes { get; set; }
        public int MemorySlots { get; set; }
        public List<string> Manufacturers { get; set; } = new List<string>();
        public string ErrorMessage { get; set; } = string.Empty;
    }

    /// <summary>
    /// 硬盘信息
    /// </summary>
    public class DiskInfo
    {
        public string SerialNumber { get; set; } = string.Empty;
        public string Model { get; set; } = string.Empty;
        public string Manufacturer { get; set; } = string.Empty;
        public long Size { get; set; }
        public string InterfaceType { get; set; } = string.Empty;
        public string MediaType { get; set; } = string.Empty;
        public string ErrorMessage { get; set; } = string.Empty;
    }

    /// <summary>
    /// 网络适配器信息
    /// </summary>
    public class NetworkAdapterInfo
    {
        public string Name { get; set; } = string.Empty;
        public string Description { get; set; } = string.Empty;
        public string MacAddress { get; set; } = string.Empty;
        public string NetworkInterfaceType { get; set; } = string.Empty;
        public string OperationalStatus { get; set; } = string.Empty;
        public long Speed { get; set; }
        public bool IsReceiveOnly { get; set; }
        public string ErrorMessage { get; set; } = string.Empty;
    }

    /// <summary>
    /// BIOS信息
    /// </summary>
    public class BiosInfo
    {
        public string Manufacturer { get; set; } = string.Empty;
        public string Version { get; set; } = string.Empty;
        public string SerialNumber { get; set; } = string.Empty;
        public DateTime? ReleaseDate { get; set; }
        public string ErrorMessage { get; set; } = string.Empty;
    }

    /// <summary>
    /// 操作系统信息
    /// </summary>
    public class OperatingSystemInfo
    {
        public string Name { get; set; } = string.Empty;
        public string Version { get; set; } = string.Empty;
        public string Architecture { get; set; } = string.Empty;
        public DateTime? InstallDate { get; set; }
        public string ErrorMessage { get; set; } = string.Empty;
    }
}
