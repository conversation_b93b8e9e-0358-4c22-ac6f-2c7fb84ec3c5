using System;
using System.ComponentModel.DataAnnotations;
using System.Linq;

namespace LicenseGenerator.Core.Models
{
    /// <summary>
    /// 客户信息模型
    /// </summary>
    public class CustomerInfo
    {
        /// <summary>
        /// 客户唯一标识符
        /// </summary>
        public Guid Id { get; set; } = Guid.NewGuid();

        /// <summary>
        /// 客户名称/联系人姓名
        /// </summary>
        [Required]
        [StringLength(100)]
        public string Name { get; set; } = string.Empty;

        /// <summary>
        /// 公司名称
        /// </summary>
        [StringLength(200)]
        public string Company { get; set; } = string.Empty;

        /// <summary>
        /// 电子邮箱
        /// </summary>
        [EmailAddress]
        [StringLength(100)]
        public string Email { get; set; } = string.Empty;

        /// <summary>
        /// 联系电话
        /// </summary>
        [Phone]
        [StringLength(20)]
        public string Phone { get; set; } = string.Empty;

        /// <summary>
        /// 地址信息
        /// </summary>
        [StringLength(500)]
        public string Address { get; set; } = string.Empty;

        /// <summary>
        /// 国家/地区
        /// </summary>
        [StringLength(50)]
        public string Country { get; set; } = string.Empty;

        /// <summary>
        /// 客户类型
        /// </summary>
        public CustomerType CustomerType { get; set; } = CustomerType.Individual;

        /// <summary>
        /// 创建时间
        /// </summary>
        public DateTime CreatedAt { get; set; } = DateTime.UtcNow;

        /// <summary>
        /// 最后更新时间
        /// </summary>
        public DateTime UpdatedAt { get; set; } = DateTime.UtcNow;

        /// <summary>
        /// 备注信息
        /// </summary>
        [StringLength(1000)]
        public string Notes { get; set; } = string.Empty;

        /// <summary>
        /// 获取显示名称 (优先显示公司名称，否则显示个人姓名)
        /// </summary>
        public string DisplayName => !string.IsNullOrWhiteSpace(Company) ? Company : Name;

        /// <summary>
        /// 获取完整联系信息
        /// </summary>
        public string FullContactInfo
        {
            get
            {
                var parts = new[]
                {
                    DisplayName,
                    !string.IsNullOrWhiteSpace(Email) ? $"Email: {Email}" : null,
                    !string.IsNullOrWhiteSpace(Phone) ? $"Phone: {Phone}" : null
                };
                return string.Join(" | ", parts.Where(p => !string.IsNullOrWhiteSpace(p)));
            }
        }
    }

    /// <summary>
    /// 客户类型枚举
    /// </summary>
    public enum CustomerType
    {
        /// <summary>
        /// 个人用户
        /// </summary>
        Individual = 0,

        /// <summary>
        /// 小型企业
        /// </summary>
        SmallBusiness = 1,

        /// <summary>
        /// 中型企业
        /// </summary>
        MediumBusiness = 2,

        /// <summary>
        /// 大型企业
        /// </summary>
        Enterprise = 3,

        /// <summary>
        /// 教育机构
        /// </summary>
        Educational = 4,

        /// <summary>
        /// 政府机构
        /// </summary>
        Government = 5,

        /// <summary>
        /// 非营利组织
        /// </summary>
        NonProfit = 6
    }
}
