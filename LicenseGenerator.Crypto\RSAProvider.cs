using System;
using System.Security.Cryptography;
using System.Text;

namespace LicenseGenerator.Crypto
{
    /// <summary>
    /// RSA加密提供者 - 负责RSA密钥生成、加密、解密和数字签名
    /// </summary>
    public class RSAProvider : IDisposable
    {
        private RSA? _rsa;
        private bool _disposed = false;

        /// <summary>
        /// 初始化RSA提供者
        /// </summary>
        /// <param name="keySize">密钥长度，默认2048位</param>
        public RSAProvider(int keySize = 2048)
        {
            _rsa = RSA.Create(keySize);
        }

        /// <summary>
        /// 使用现有密钥初始化RSA提供者
        /// </summary>
        /// <param name="privateKeyPem">私钥PEM格式字符串</param>
        public RSAProvider(string privateKeyPem)
        {
            _rsa = RSA.Create();
            ImportPrivateKeyFromPem(privateKeyPem);
        }

        /// <summary>
        /// 生成RSA密钥对
        /// </summary>
        /// <param name="keySize">密钥长度</param>
        /// <returns>包含公钥和私钥的密钥对</returns>
        public static RSAKeyPair GenerateKeyPair(int keySize = 2048)
        {
            using var rsa = RSA.Create(keySize);

            var publicKeyPem = ExportPublicKeyToPem(rsa);
            var privateKeyPem = ExportPrivateKeyToPem(rsa);

            return new RSAKeyPair
            {
                PublicKey = publicKeyPem,
                PrivateKey = privateKeyPem,
                KeySize = keySize,
                CreatedAt = DateTime.UtcNow
            };
        }

        /// <summary>
        /// 从PEM格式导入私钥
        /// </summary>
        /// <param name="privateKeyPem">私钥PEM字符串</param>
        public void ImportPrivateKeyFromPem(string privateKeyPem)
        {
            ThrowIfDisposed();
            _rsa?.ImportFromPem(privateKeyPem);
        }

        /// <summary>
        /// 从PEM格式导入公钥
        /// </summary>
        /// <param name="publicKeyPem">公钥PEM字符串</param>
        public void ImportPublicKeyFromPem(string publicKeyPem)
        {
            ThrowIfDisposed();
            _rsa?.ImportFromPem(publicKeyPem);
        }

        /// <summary>
        /// 导出公钥为PEM格式
        /// </summary>
        /// <returns>公钥PEM字符串</returns>
        public string ExportPublicKeyToPem()
        {
            ThrowIfDisposed();
            return ExportPublicKeyToPem(_rsa ?? throw new InvalidOperationException("RSA instance is null"));
        }

        /// <summary>
        /// 导出私钥为PEM格式
        /// </summary>
        /// <returns>私钥PEM字符串</returns>
        public string ExportPrivateKeyToPem()
        {
            ThrowIfDisposed();
            return ExportPrivateKeyToPem(_rsa ?? throw new InvalidOperationException("RSA instance is null"));
        }

        /// <summary>
        /// RSA加密数据
        /// </summary>
        /// <param name="data">要加密的数据</param>
        /// <param name="padding">填充模式</param>
        /// <returns>加密后的数据</returns>
        public byte[] Encrypt(byte[] data, RSAEncryptionPadding? padding = null)
        {
            ThrowIfDisposed();
            padding ??= RSAEncryptionPadding.OaepSHA256;
            return _rsa?.Encrypt(data, padding) ?? throw new InvalidOperationException("RSA instance is null");
        }

        /// <summary>
        /// RSA加密字符串
        /// </summary>
        /// <param name="plainText">要加密的字符串</param>
        /// <param name="padding">填充模式</param>
        /// <returns>Base64编码的加密数据</returns>
        public string EncryptString(string plainText, RSAEncryptionPadding? padding = null)
        {
            var data = Encoding.UTF8.GetBytes(plainText);
            var encryptedData = Encrypt(data, padding);
            return Convert.ToBase64String(encryptedData);
        }

        /// <summary>
        /// RSA解密数据
        /// </summary>
        /// <param name="encryptedData">加密的数据</param>
        /// <param name="padding">填充模式</param>
        /// <returns>解密后的数据</returns>
        public byte[] Decrypt(byte[] encryptedData, RSAEncryptionPadding? padding = null)
        {
            ThrowIfDisposed();
            padding ??= RSAEncryptionPadding.OaepSHA256;
            return _rsa?.Decrypt(encryptedData, padding) ?? throw new InvalidOperationException("RSA instance is null");
        }

        /// <summary>
        /// RSA解密字符串
        /// </summary>
        /// <param name="encryptedText">Base64编码的加密数据</param>
        /// <param name="padding">填充模式</param>
        /// <returns>解密后的字符串</returns>
        public string DecryptString(string encryptedText, RSAEncryptionPadding? padding = null)
        {
            var encryptedData = Convert.FromBase64String(encryptedText);
            var data = Decrypt(encryptedData, padding);
            return Encoding.UTF8.GetString(data);
        }

        /// <summary>
        /// 创建数字签名
        /// </summary>
        /// <param name="data">要签名的数据</param>
        /// <param name="hashAlgorithm">哈希算法</param>
        /// <param name="padding">签名填充模式</param>
        /// <returns>数字签名</returns>
        public byte[] SignData(byte[] data, HashAlgorithmName hashAlgorithm = default, RSASignaturePadding? padding = null)
        {
            ThrowIfDisposed();
            hashAlgorithm = hashAlgorithm == default ? HashAlgorithmName.SHA256 : hashAlgorithm;
            padding ??= RSASignaturePadding.Pkcs1;
            return _rsa?.SignData(data, hashAlgorithm, padding) ?? throw new InvalidOperationException("RSA instance is null");
        }

        /// <summary>
        /// 创建字符串的数字签名
        /// </summary>
        /// <param name="text">要签名的字符串</param>
        /// <param name="hashAlgorithm">哈希算法</param>
        /// <param name="padding">签名填充模式</param>
        /// <returns>Base64编码的数字签名</returns>
        public string SignString(string text, HashAlgorithmName hashAlgorithm = default, RSASignaturePadding? padding = null)
        {
            var data = Encoding.UTF8.GetBytes(text);
            var signature = SignData(data, hashAlgorithm, padding);
            return Convert.ToBase64String(signature);
        }

        /// <summary>
        /// 验证数字签名
        /// </summary>
        /// <param name="data">原始数据</param>
        /// <param name="signature">数字签名</param>
        /// <param name="hashAlgorithm">哈希算法</param>
        /// <param name="padding">签名填充模式</param>
        /// <returns>签名是否有效</returns>
        public bool VerifyData(byte[] data, byte[] signature, HashAlgorithmName hashAlgorithm = default, RSASignaturePadding? padding = null)
        {
            ThrowIfDisposed();
            hashAlgorithm = hashAlgorithm == default ? HashAlgorithmName.SHA256 : hashAlgorithm;
            padding ??= RSASignaturePadding.Pkcs1;
            return _rsa?.VerifyData(data, signature, hashAlgorithm, padding) ?? false;
        }

        /// <summary>
        /// 验证字符串的数字签名
        /// </summary>
        /// <param name="text">原始字符串</param>
        /// <param name="signatureBase64">Base64编码的数字签名</param>
        /// <param name="hashAlgorithm">哈希算法</param>
        /// <param name="padding">签名填充模式</param>
        /// <returns>签名是否有效</returns>
        public bool VerifyString(string text, string signatureBase64, HashAlgorithmName hashAlgorithm = default, RSASignaturePadding? padding = null)
        {
            var data = Encoding.UTF8.GetBytes(text);
            var signature = Convert.FromBase64String(signatureBase64);
            return VerifyData(data, signature, hashAlgorithm, padding);
        }

        /// <summary>
        /// 获取密钥大小
        /// </summary>
        public int KeySize => _rsa?.KeySize ?? 0;

        /// <summary>
        /// 获取公钥指纹 (SHA256哈希)
        /// </summary>
        /// <returns>公钥指纹</returns>
        public string GetPublicKeyFingerprint()
        {
            ThrowIfDisposed();
            var publicKeyPem = ExportPublicKeyToPem();
            using var sha256 = SHA256.Create();
            var hash = sha256.ComputeHash(Encoding.UTF8.GetBytes(publicKeyPem));
            return Convert.ToHexString(hash).ToLowerInvariant();
        }

        /// <summary>
        /// 导出RSA公钥为PEM格式 (静态方法)
        /// </summary>
        /// <param name="rsa">RSA实例</param>
        /// <returns>公钥PEM字符串</returns>
        private static string ExportPublicKeyToPem(RSA rsa)
        {
            var publicKeyBytes = rsa.ExportRSAPublicKey();
            return "-----BEGIN RSA PUBLIC KEY-----\n" +
                   Convert.ToBase64String(publicKeyBytes, Base64FormattingOptions.InsertLineBreaks) +
                   "\n-----END RSA PUBLIC KEY-----";
        }

        /// <summary>
        /// 导出RSA私钥为PEM格式 (静态方法)
        /// </summary>
        /// <param name="rsa">RSA实例</param>
        /// <returns>私钥PEM字符串</returns>
        private static string ExportPrivateKeyToPem(RSA rsa)
        {
            var privateKeyBytes = rsa.ExportRSAPrivateKey();
            return "-----BEGIN RSA PRIVATE KEY-----\n" +
                   Convert.ToBase64String(privateKeyBytes, Base64FormattingOptions.InsertLineBreaks) +
                   "\n-----END RSA PRIVATE KEY-----";
        }

        private void ThrowIfDisposed()
        {
            if (_disposed)
                throw new ObjectDisposedException(nameof(RSAProvider));
        }

        public void Dispose()
        {
            if (!_disposed)
            {
                _rsa?.Dispose();
                _rsa = null;
                _disposed = true;
            }
        }
    }

    /// <summary>
    /// RSA密钥对
    /// </summary>
    public class RSAKeyPair
    {
        /// <summary>
        /// 公钥 (PEM格式)
        /// </summary>
        public string PublicKey { get; set; } = string.Empty;

        /// <summary>
        /// 私钥 (PEM格式)
        /// </summary>
        public string PrivateKey { get; set; } = string.Empty;

        /// <summary>
        /// 密钥长度
        /// </summary>
        public int KeySize { get; set; }

        /// <summary>
        /// 创建时间
        /// </summary>
        public DateTime CreatedAt { get; set; }

        /// <summary>
        /// 获取公钥指纹
        /// </summary>
        /// <returns>公钥指纹</returns>
        public string GetFingerprint()
        {
            using var sha256 = SHA256.Create();
            var hash = sha256.ComputeHash(Encoding.UTF8.GetBytes(PublicKey));
            return Convert.ToHexString(hash).ToLowerInvariant();
        }
    }
}
