using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;

namespace LicenseGenerator.Core.Models
{
    /// <summary>
    /// License信息模型 - 包含完整的授权信息
    /// </summary>
    public class LicenseInfo
    {
        /// <summary>
        /// License唯一标识符
        /// </summary>
        public Guid Id { get; set; } = Guid.NewGuid();

        /// <summary>
        /// 目标应用程序标识
        /// </summary>
        [Required]
        [StringLength(100)]
        public string AppId { get; set; } = string.Empty;

        /// <summary>
        /// 应用程序名称
        /// </summary>
        [StringLength(200)]
        public string AppName { get; set; } = string.Empty;

        /// <summary>
        /// 设备指纹信息
        /// </summary>
        [Required]
        public string DeviceFingerprint { get; set; } = string.Empty;

        /// <summary>
        /// License类型
        /// </summary>
        public LicenseType LicenseType { get; set; } = LicenseType.Trial;

        /// <summary>
        /// 授权功能列表
        /// </summary>
        public List<string> Features { get; set; } = new List<string>();

        /// <summary>
        /// License颁发日期
        /// </summary>
        public DateTime IssueDate { get; set; } = DateTime.UtcNow;

        /// <summary>
        /// License过期日期 (null表示永不过期)
        /// </summary>
        public DateTime? ExpiryDate { get; set; }

        /// <summary>
        /// 最大并发用户数 (0表示无限制)
        /// </summary>
        public int MaxConcurrentUsers { get; set; } = 1;

        /// <summary>
        /// 客户信息
        /// </summary>
        public CustomerInfo Customer { get; set; } = new CustomerInfo();

        /// <summary>
        /// 数字签名
        /// </summary>
        public string DigitalSignature { get; set; } = string.Empty;

        /// <summary>
        /// License状态
        /// </summary>
        public LicenseStatus Status { get; set; } = LicenseStatus.Active;

        /// <summary>
        /// 创建时间
        /// </summary>
        public DateTime CreatedAt { get; set; } = DateTime.UtcNow;

        /// <summary>
        /// 最后修改时间
        /// </summary>
        public DateTime UpdatedAt { get; set; } = DateTime.UtcNow;

        /// <summary>
        /// 备注信息
        /// </summary>
        [StringLength(1000)]
        public string Notes { get; set; } = string.Empty;

        /// <summary>
        /// 检查License是否已过期
        /// </summary>
        public bool IsExpired => ExpiryDate.HasValue && ExpiryDate.Value < DateTime.UtcNow;

        /// <summary>
        /// 检查License是否有效
        /// </summary>
        public bool IsValid => Status == LicenseStatus.Active && !IsExpired;

        /// <summary>
        /// 获取剩余有效天数
        /// </summary>
        public int? RemainingDays
        {
            get
            {
                if (!ExpiryDate.HasValue) return null;
                var remaining = (ExpiryDate.Value - DateTime.UtcNow).Days;
                return remaining > 0 ? remaining : 0;
            }
        }
    }

    /// <summary>
    /// License类型枚举
    /// </summary>
    public enum LicenseType
    {
        /// <summary>
        /// 试用版
        /// </summary>
        Trial = 0,

        /// <summary>
        /// 标准版
        /// </summary>
        Standard = 1,

        /// <summary>
        /// 专业版
        /// </summary>
        Professional = 2,

        /// <summary>
        /// 企业版
        /// </summary>
        Enterprise = 3,

        /// <summary>
        /// 开发者版
        /// </summary>
        Developer = 4,

        /// <summary>
        /// 自定义版本
        /// </summary>
        Custom = 99
    }

    /// <summary>
    /// License状态枚举
    /// </summary>
    public enum LicenseStatus
    {
        /// <summary>
        /// 激活状态
        /// </summary>
        Active = 0,

        /// <summary>
        /// 已暂停
        /// </summary>
        Suspended = 1,

        /// <summary>
        /// 已撤销
        /// </summary>
        Revoked = 2,

        /// <summary>
        /// 已过期
        /// </summary>
        Expired = 3
    }
}
