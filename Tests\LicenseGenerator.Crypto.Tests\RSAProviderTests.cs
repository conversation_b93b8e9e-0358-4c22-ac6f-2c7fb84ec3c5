using System;
using System.Security.Cryptography;
using System.Text;
using LicenseGenerator.Crypto;
using Xunit;

namespace LicenseGenerator.Crypto.Tests
{
    /// <summary>
    /// RSA提供者测试
    /// </summary>
    public class RSAProviderTests : IDisposable
    {
        private RSAProvider? _rsaProvider;

        public RSAProviderTests()
        {
            _rsaProvider = new RSAProvider(2048);
        }

        [Fact]
        public void GenerateKeyPair_ShouldCreateValidKeyPair()
        {
            // Arrange & Act
            var keyPair = RSAProvider.GenerateKeyPair(2048);

            // Assert
            Assert.NotNull(keyPair);
            Assert.NotEmpty(keyPair.PublicKey);
            Assert.NotEmpty(keyPair.PrivateKey);
            Assert.Equal(2048, keyPair.KeySize);
            Assert.True(keyPair.CreatedAt <= DateTime.UtcNow);
            Assert.Contains("-----BEGIN RSA PUBLIC KEY-----", keyPair.PublicKey);
            Assert.Contains("-----BEGIN RSA PRIVATE KEY-----", keyPair.PrivateKey);
        }

        [Fact]
        public void GetFingerprint_ShouldReturnConsistentFingerprint()
        {
            // Arrange
            var keyPair = RSAProvider.GenerateKeyPair(2048);

            // Act
            var fingerprint1 = keyPair.GetFingerprint();
            var fingerprint2 = keyPair.GetFingerprint();

            // Assert
            Assert.NotEmpty(fingerprint1);
            Assert.Equal(fingerprint1, fingerprint2);
            Assert.Equal(64, fingerprint1.Length); // SHA256 hex string length
        }

        [Fact]
        public void EncryptDecrypt_ShouldWorkCorrectly()
        {
            // Arrange
            var plainText = "Hello, World! This is a test message for RSA encryption.";

            // Act
            var encryptedText = _rsaProvider!.EncryptString(plainText);
            var decryptedText = _rsaProvider.DecryptString(encryptedText);

            // Assert
            Assert.NotEqual(plainText, encryptedText);
            Assert.Equal(plainText, decryptedText);
        }

        [Fact]
        public void SignVerify_ShouldWorkCorrectly()
        {
            // Arrange
            var message = "This is a test message for digital signature.";

            // Act
            var signature = _rsaProvider!.SignString(message);
            var isValid = _rsaProvider.VerifyString(message, signature);

            // Assert
            Assert.NotEmpty(signature);
            Assert.True(isValid);
        }

        [Fact]
        public void SignVerify_WithDifferentMessage_ShouldFail()
        {
            // Arrange
            var originalMessage = "Original message";
            var modifiedMessage = "Modified message";

            // Act
            var signature = _rsaProvider!.SignString(originalMessage);
            var isValid = _rsaProvider.VerifyString(modifiedMessage, signature);

            // Assert
            Assert.False(isValid);
        }

        [Fact]
        public void ExportImportKeys_ShouldWorkCorrectly()
        {
            // Arrange
            var originalMessage = "Test message for key export/import";
            var originalSignature = _rsaProvider!.SignString(originalMessage);

            // Act
            var publicKeyPem = _rsaProvider.ExportPublicKeyToPem();
            var privateKeyPem = _rsaProvider.ExportPrivateKeyToPem();

            using var newRsaProvider = new RSAProvider(privateKeyPem);
            var newSignature = newRsaProvider.SignString(originalMessage);
            var isOriginalValid = newRsaProvider.VerifyString(originalMessage, originalSignature);
            var isNewValid = newRsaProvider.VerifyString(originalMessage, newSignature);

            // Assert
            Assert.NotEmpty(publicKeyPem);
            Assert.NotEmpty(privateKeyPem);
            Assert.True(isOriginalValid);
            Assert.True(isNewValid);
        }

        [Fact]
        public void GetPublicKeyFingerprint_ShouldReturnValidFingerprint()
        {
            // Act
            var fingerprint = _rsaProvider!.GetPublicKeyFingerprint();

            // Assert
            Assert.NotEmpty(fingerprint);
            Assert.Equal(64, fingerprint.Length); // SHA256 hex string length
            Assert.True(fingerprint.All(c => char.IsDigit(c) || (c >= 'a' && c <= 'f')));
        }

        [Fact]
        public void KeySize_ShouldReturnCorrectSize()
        {
            // Act
            var keySize = _rsaProvider!.KeySize;

            // Assert
            Assert.Equal(2048, keySize);
        }

        [Theory]
        [InlineData(1024)]
        [InlineData(2048)]
        [InlineData(4096)]
        public void GenerateKeyPair_WithDifferentSizes_ShouldWork(int keySize)
        {
            // Act
            var keyPair = RSAProvider.GenerateKeyPair(keySize);

            // Assert
            Assert.Equal(keySize, keyPair.KeySize);
            
            // Test that the key actually works
            using var rsa = new RSAProvider(keyPair.PrivateKey);
            Assert.Equal(keySize, rsa.KeySize);
        }

        [Fact]
        public void EncryptDecrypt_WithLargeData_ShouldHandleCorrectly()
        {
            // Arrange
            // RSA can only encrypt data smaller than key size minus padding
            // For 2048-bit key with OAEP padding, max is about 190 bytes
            var plainText = "Short message"; // Keep it small for RSA

            // Act & Assert
            var encryptedText = _rsaProvider!.EncryptString(plainText);
            var decryptedText = _rsaProvider.DecryptString(encryptedText);
            
            Assert.Equal(plainText, decryptedText);
        }

        [Fact]
        public void SignVerify_WithDifferentHashAlgorithms_ShouldWork()
        {
            // Arrange
            var message = "Test message for different hash algorithms";

            // Act & Assert for SHA256
            var signatureSHA256 = _rsaProvider!.SignString(message, HashAlgorithmName.SHA256);
            var isValidSHA256 = _rsaProvider.VerifyString(message, signatureSHA256, HashAlgorithmName.SHA256);
            Assert.True(isValidSHA256);

            // Act & Assert for SHA512
            var signatureSHA512 = _rsaProvider.SignString(message, HashAlgorithmName.SHA512);
            var isValidSHA512 = _rsaProvider.VerifyString(message, signatureSHA512, HashAlgorithmName.SHA512);
            Assert.True(isValidSHA512);

            // Cross-verification should fail
            var crossValid = _rsaProvider.VerifyString(message, signatureSHA256, HashAlgorithmName.SHA512);
            Assert.False(crossValid);
        }

        public void Dispose()
        {
            _rsaProvider?.Dispose();
        }
    }
}
