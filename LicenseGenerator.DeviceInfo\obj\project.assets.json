{"version": 3, "targets": {"net6.0": {"System.CodeDom/9.0.6": {"type": "package", "compile": {"lib/netstandard2.0/System.CodeDom.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/System.CodeDom.dll": {"related": ".xml"}}, "build": {"buildTransitive/netcoreapp2.0/System.CodeDom.targets": {}}}, "System.Management/9.0.6": {"type": "package", "dependencies": {"System.CodeDom": "9.0.6"}, "compile": {"lib/netstandard2.0/System.Management.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/System.Management.dll": {"related": ".xml"}}, "build": {"buildTransitive/netcoreapp2.0/System.Management.targets": {}}}}}, "libraries": {"System.CodeDom/9.0.6": {"sha512": "9u1pyEykc0bWBHf1cIVwRoMqrEtxtXdC2ss1K02pvrkwHPcyYmy1glO+kLbyqPO9ehCTl+2dFyUuTUNl1Fde5g==", "type": "package", "path": "system.codedom/9.0.6", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/System.CodeDom.targets", "buildTransitive/net462/_._", "buildTransitive/net8.0/_._", "buildTransitive/netcoreapp2.0/System.CodeDom.targets", "lib/net462/System.CodeDom.dll", "lib/net462/System.CodeDom.xml", "lib/net8.0/System.CodeDom.dll", "lib/net8.0/System.CodeDom.xml", "lib/net9.0/System.CodeDom.dll", "lib/net9.0/System.CodeDom.xml", "lib/netstandard2.0/System.CodeDom.dll", "lib/netstandard2.0/System.CodeDom.xml", "system.codedom.9.0.6.nupkg.sha512", "system.codedom.nuspec", "useSharedDesignerContext.txt"]}, "System.Management/9.0.6": {"sha512": "pOc5bCurWL3qVDsPP0iycUCRfXBhI/fVe44SiAlVqoZDIbHP080CLyCzfXV1UbdGKN0hQSLSHqr7OI3BhLBRbA==", "type": "package", "path": "system.management/9.0.6", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net8.0/_._", "buildTransitive/netcoreapp2.0/System.Management.targets", "lib/net462/_._", "lib/net8.0/System.Management.dll", "lib/net8.0/System.Management.xml", "lib/net9.0/System.Management.dll", "lib/net9.0/System.Management.xml", "lib/netstandard2.0/System.Management.dll", "lib/netstandard2.0/System.Management.xml", "runtimes/win/lib/net8.0/System.Management.dll", "runtimes/win/lib/net8.0/System.Management.xml", "runtimes/win/lib/net9.0/System.Management.dll", "runtimes/win/lib/net9.0/System.Management.xml", "system.management.9.0.6.nupkg.sha512", "system.management.nuspec", "useSharedDesignerContext.txt"]}}, "projectFileDependencyGroups": {"net6.0": ["System.Management >= 9.0.6"]}, "packageFolders": {"C:\\Users\\<USER>\\.nuget\\packages\\": {}, "E:\\DevExpress 23.2\\Components\\Offline Packages": {}, "e:\\DevExpress 24.2\\Components\\Offline Packages": {}, "E:\\Microsoft Visual Studio\\Shared\\NuGetPackages": {}, "D:\\Syncfusion\\Essential Studio\\WPF\\29.1.33\\ToolboxNuGetPackages": {}}, "project": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\00 Crack\\LicenseGenerator.DeviceInfo\\LicenseGenerator.DeviceInfo.csproj", "projectName": "LicenseGenerator.DeviceInfo", "projectPath": "D:\\00 Crack\\LicenseGenerator.DeviceInfo\\LicenseGenerator.DeviceInfo.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\00 Crack\\LicenseGenerator.DeviceInfo\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["E:\\DevExpress 23.2\\Components\\Offline Packages", "e:\\DevExpress 24.2\\Components\\Offline Packages", "E:\\Microsoft Visual Studio\\Shared\\NuGetPackages", "D:\\Syncfusion\\Essential Studio\\WPF\\29.1.33\\ToolboxNuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\DevExpress 23.2.config", "C:\\Program Files (x86)\\NuGet\\Config\\DevExpress 24.2.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config", "C:\\Program Files (x86)\\NuGet\\Config\\Syncfusion Toolbox for WPF.config"], "originalTargetFrameworks": ["net6.0"], "sources": {"C:\\Program Files\\dotnet\\library-packs": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net6.0": {"targetAlias": "net6.0", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net6.0": {"targetAlias": "net6.0", "dependencies": {"System.Management": {"target": "Package", "version": "[9.0.6, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.300\\RuntimeIdentifierGraph.json"}}}}