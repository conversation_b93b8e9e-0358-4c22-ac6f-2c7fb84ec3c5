using System;
using System.Collections.Generic;
using System.Linq;
using System.Security.Cryptography;
using System.Text;
using System.Text.Json;

namespace LicenseGenerator.DeviceInfo
{
    /// <summary>
    /// 设备标识符生成器 - 提供多种设备标识符生成策略
    /// </summary>
    public static class DeviceIdentifier
    {
        /// <summary>
        /// 生成简单设备ID (基于主要硬件组件)
        /// </summary>
        /// <returns>设备ID</returns>
        public static string GenerateSimpleDeviceId()
        {
            var components = new List<string>();

            try
            {
                // CPU ID
                var cpuInfo = HardwareInfoProvider.GetCpuInfo();
                if (!string.IsNullOrEmpty(cpuInfo.ProcessorId))
                {
                    components.Add(cpuInfo.ProcessorId);
                }

                // 主板序列号
                var motherboardInfo = HardwareInfoProvider.GetMotherboardInfo();
                if (!string.IsNullOrEmpty(motherboardInfo.SerialNumber))
                {
                    components.Add(motherboardInfo.SerialNumber);
                }

                // 主要MAC地址
                var networkAdapters = HardwareInfoProvider.GetNetworkAdapterInfo();
                var primaryMac = networkAdapters
                    .Where(n => !string.IsNullOrEmpty(n.MacAddress) && n.MacAddress != "000000000000")
                    .OrderBy(n => n.Name)
                    .FirstOrDefault()?.MacAddress;
                
                if (!string.IsNullOrEmpty(primaryMac))
                {
                    components.Add(primaryMac);
                }

                // 如果组件不足，添加备用信息
                if (components.Count < 2)
                {
                    components.Add(Environment.MachineName);
                    components.Add(Environment.UserDomainName);
                }
            }
            catch
            {
                // 如果硬件信息获取失败，使用环境信息
                components.Add(Environment.MachineName);
                components.Add(Environment.UserDomainName);
                components.Add(Environment.ProcessorCount.ToString());
            }

            var combinedData = string.Join("|", components.Where(c => !string.IsNullOrEmpty(c)));
            return GenerateHash(combinedData, HashAlgorithm.SHA256);
        }

        /// <summary>
        /// 生成强设备ID (包含更多硬件信息)
        /// </summary>
        /// <returns>设备ID</returns>
        public static string GenerateStrongDeviceId()
        {
            var collector = new DeviceFingerprintCollector();
            var deviceInfo = collector.CollectDeviceInformation();
            return deviceInfo.DeviceFingerprint;
        }

        /// <summary>
        /// 生成自定义设备ID
        /// </summary>
        /// <param name="components">指定的组件</param>
        /// <param name="hashAlgorithm">哈希算法</param>
        /// <returns>设备ID</returns>
        public static string GenerateCustomDeviceId(IEnumerable<string> components, HashAlgorithm hashAlgorithm = HashAlgorithm.SHA256)
        {
            var validComponents = components.Where(c => !string.IsNullOrWhiteSpace(c)).ToList();
            if (!validComponents.Any())
            {
                throw new ArgumentException("至少需要一个有效的组件", nameof(components));
            }

            var combinedData = string.Join("|", validComponents);
            return GenerateHash(combinedData, hashAlgorithm);
        }

        /// <summary>
        /// 生成基于时间的设备ID (包含时间戳)
        /// </summary>
        /// <param name="includeDate">是否包含日期</param>
        /// <param name="includeTime">是否包含时间</param>
        /// <returns>设备ID</returns>
        public static string GenerateTimestampedDeviceId(bool includeDate = true, bool includeTime = false)
        {
            var components = new List<string>();

            // 添加基本硬件信息
            try
            {
                var cpuInfo = HardwareInfoProvider.GetCpuInfo();
                if (!string.IsNullOrEmpty(cpuInfo.ProcessorId))
                {
                    components.Add(cpuInfo.ProcessorId);
                }

                var motherboardInfo = HardwareInfoProvider.GetMotherboardInfo();
                if (!string.IsNullOrEmpty(motherboardInfo.SerialNumber))
                {
                    components.Add(motherboardInfo.SerialNumber);
                }
            }
            catch
            {
                components.Add(Environment.MachineName);
            }

            // 添加时间戳
            var now = DateTime.UtcNow;
            if (includeDate)
            {
                components.Add(now.ToString("yyyy-MM-dd"));
            }
            if (includeTime)
            {
                components.Add(now.ToString("HH:mm"));
            }

            var combinedData = string.Join("|", components);
            return GenerateHash(combinedData, HashAlgorithm.SHA256);
        }

        /// <summary>
        /// 生成网络设备ID (基于网络适配器)
        /// </summary>
        /// <param name="includeAllAdapters">是否包含所有适配器</param>
        /// <returns>设备ID</returns>
        public static string GenerateNetworkDeviceId(bool includeAllAdapters = false)
        {
            var networkAdapters = HardwareInfoProvider.GetNetworkAdapterInfo();
            var validAdapters = networkAdapters
                .Where(n => !string.IsNullOrEmpty(n.MacAddress) && n.MacAddress != "000000000000")
                .ToList();

            if (!validAdapters.Any())
            {
                throw new InvalidOperationException("未找到有效的网络适配器");
            }

            var components = new List<string>();

            if (includeAllAdapters)
            {
                // 包含所有适配器的MAC地址
                components.AddRange(validAdapters.OrderBy(n => n.MacAddress).Select(n => n.MacAddress));
            }
            else
            {
                // 只包含主要适配器
                var primaryAdapter = validAdapters.OrderBy(n => n.Name).First();
                components.Add(primaryAdapter.MacAddress);
            }

            var combinedData = string.Join("|", components);
            return GenerateHash(combinedData, HashAlgorithm.SHA256);
        }

        /// <summary>
        /// 生成存储设备ID (基于硬盘信息)
        /// </summary>
        /// <param name="includeAllDisks">是否包含所有硬盘</param>
        /// <returns>设备ID</returns>
        public static string GenerateStorageDeviceId(bool includeAllDisks = false)
        {
            var diskInfoList = HardwareInfoProvider.GetDiskInfo();
            var validDisks = diskInfoList
                .Where(d => !string.IsNullOrEmpty(d.SerialNumber))
                .ToList();

            if (!validDisks.Any())
            {
                throw new InvalidOperationException("未找到有效的存储设备");
            }

            var components = new List<string>();

            if (includeAllDisks)
            {
                // 包含所有硬盘序列号
                components.AddRange(validDisks.OrderBy(d => d.SerialNumber).Select(d => d.SerialNumber));
            }
            else
            {
                // 只包含主要硬盘
                var primaryDisk = validDisks.OrderBy(d => d.Model).First();
                components.Add(primaryDisk.SerialNumber);
            }

            var combinedData = string.Join("|", components);
            return GenerateHash(combinedData, HashAlgorithm.SHA256);
        }

        /// <summary>
        /// 验证设备ID
        /// </summary>
        /// <param name="deviceId">要验证的设备ID</param>
        /// <param name="generationMethod">生成方法</param>
        /// <returns>验证结果</returns>
        public static DeviceIdValidationResult ValidateDeviceId(string deviceId, DeviceIdGenerationMethod generationMethod)
        {
            var result = new DeviceIdValidationResult
            {
                ProvidedDeviceId = deviceId,
                GenerationMethod = generationMethod,
                ValidationTime = DateTime.UtcNow
            };

            try
            {
                string currentDeviceId = generationMethod switch
                {
                    DeviceIdGenerationMethod.Simple => GenerateSimpleDeviceId(),
                    DeviceIdGenerationMethod.Strong => GenerateStrongDeviceId(),
                    DeviceIdGenerationMethod.Network => GenerateNetworkDeviceId(),
                    DeviceIdGenerationMethod.Storage => GenerateStorageDeviceId(),
                    _ => throw new ArgumentException("不支持的生成方法", nameof(generationMethod))
                };

                result.CurrentDeviceId = currentDeviceId;
                result.IsMatch = string.Equals(deviceId, currentDeviceId, StringComparison.OrdinalIgnoreCase);
                result.Success = true;
            }
            catch (Exception ex)
            {
                result.Success = false;
                result.ErrorMessage = ex.Message;
            }

            return result;
        }

        /// <summary>
        /// 生成设备ID信息摘要
        /// </summary>
        /// <returns>设备ID信息</returns>
        public static DeviceIdInfo GenerateDeviceIdInfo()
        {
            var info = new DeviceIdInfo
            {
                GeneratedAt = DateTime.UtcNow
            };

            try
            {
                info.SimpleDeviceId = GenerateSimpleDeviceId();
                info.StrongDeviceId = GenerateStrongDeviceId();
                
                try
                {
                    info.NetworkDeviceId = GenerateNetworkDeviceId();
                }
                catch
                {
                    info.NetworkDeviceId = "N/A";
                }

                try
                {
                    info.StorageDeviceId = GenerateStorageDeviceId();
                }
                catch
                {
                    info.StorageDeviceId = "N/A";
                }

                info.MachineInfo = new MachineInfo
                {
                    MachineName = Environment.MachineName,
                    UserDomainName = Environment.UserDomainName,
                    ProcessorCount = Environment.ProcessorCount,
                    OSVersion = Environment.OSVersion.ToString(),
                    Is64BitOperatingSystem = Environment.Is64BitOperatingSystem,
                    Is64BitProcess = Environment.Is64BitProcess
                };

                info.Success = true;
            }
            catch (Exception ex)
            {
                info.Success = false;
                info.ErrorMessage = ex.Message;
            }

            return info;
        }

        /// <summary>
        /// 生成哈希值
        /// </summary>
        /// <param name="input">输入字符串</param>
        /// <param name="algorithm">哈希算法</param>
        /// <returns>哈希值</returns>
        private static string GenerateHash(string input, HashAlgorithm algorithm)
        {
            System.Security.Cryptography.HashAlgorithm hash = algorithm switch
            {
                HashAlgorithm.MD5 => MD5.Create(),
                HashAlgorithm.SHA1 => SHA1.Create(),
                HashAlgorithm.SHA256 => SHA256.Create(),
                HashAlgorithm.SHA384 => SHA384.Create(),
                HashAlgorithm.SHA512 => SHA512.Create(),
                _ => SHA256.Create()
            };

            using (hash)
            {
                var hashBytes = hash.ComputeHash(Encoding.UTF8.GetBytes(input));
                return Convert.ToHexString(hashBytes).ToLowerInvariant();
            }
        }

        /// <summary>
        /// 导出设备ID信息为JSON
        /// </summary>
        /// <param name="deviceIdInfo">设备ID信息</param>
        /// <returns>JSON字符串</returns>
        public static string ExportToJson(DeviceIdInfo deviceIdInfo)
        {
            var options = new JsonSerializerOptions
            {
                WriteIndented = true,
                PropertyNamingPolicy = JsonNamingPolicy.CamelCase
            };

            return JsonSerializer.Serialize(deviceIdInfo, options);
        }
    }

    /// <summary>
    /// 哈希算法枚举
    /// </summary>
    public enum HashAlgorithm
    {
        MD5,
        SHA1,
        SHA256,
        SHA384,
        SHA512
    }

    /// <summary>
    /// 设备ID生成方法枚举
    /// </summary>
    public enum DeviceIdGenerationMethod
    {
        Simple,
        Strong,
        Network,
        Storage,
        Custom
    }

    /// <summary>
    /// 设备ID验证结果
    /// </summary>
    public class DeviceIdValidationResult
    {
        public bool Success { get; set; }
        public bool IsMatch { get; set; }
        public string ProvidedDeviceId { get; set; } = string.Empty;
        public string CurrentDeviceId { get; set; } = string.Empty;
        public DeviceIdGenerationMethod GenerationMethod { get; set; }
        public DateTime ValidationTime { get; set; }
        public string ErrorMessage { get; set; } = string.Empty;
    }

    /// <summary>
    /// 设备ID信息
    /// </summary>
    public class DeviceIdInfo
    {
        public bool Success { get; set; }
        public string SimpleDeviceId { get; set; } = string.Empty;
        public string StrongDeviceId { get; set; } = string.Empty;
        public string NetworkDeviceId { get; set; } = string.Empty;
        public string StorageDeviceId { get; set; } = string.Empty;
        public MachineInfo? MachineInfo { get; set; }
        public DateTime GeneratedAt { get; set; }
        public string ErrorMessage { get; set; } = string.Empty;
    }

    /// <summary>
    /// 机器信息
    /// </summary>
    public class MachineInfo
    {
        public string MachineName { get; set; } = string.Empty;
        public string UserDomainName { get; set; } = string.Empty;
        public int ProcessorCount { get; set; }
        public string OSVersion { get; set; } = string.Empty;
        public bool Is64BitOperatingSystem { get; set; }
        public bool Is64BitProcess { get; set; }
    }
}
