using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Security.Cryptography;
using System.Text;
using System.Text.Json;

namespace LicenseGenerator.Crypto
{
    /// <summary>
    /// 密钥管理器 - 负责密钥的生成、存储、加载和管理
    /// </summary>
    public class KeyManager : IDisposable
    {
        private readonly string _keyStorePath;
        private readonly Dictionary<Guid, CachedKeyPair> _keyCache;
        private bool _disposed = false;

        /// <summary>
        /// 初始化密钥管理器
        /// </summary>
        /// <param name="keyStorePath">密钥存储路径</param>
        public KeyManager(string keyStorePath)
        {
            _keyStorePath = keyStorePath ?? throw new ArgumentNullException(nameof(keyStorePath));
            _keyCache = new Dictionary<Guid, CachedKeyPair>();
            
            // 确保密钥存储目录存在
            if (!Directory.Exists(_keyStorePath))
            {
                Directory.CreateDirectory(_keyStorePath);
            }
        }

        /// <summary>
        /// 生成新的RSA密钥对
        /// </summary>
        /// <param name="name">密钥对名称</param>
        /// <param name="keySize">密钥长度</param>
        /// <param name="password">密码保护 (可选)</param>
        /// <returns>密钥对信息</returns>
        public KeyPairInfo GenerateKeyPair(string name, int keySize = 2048, string? password = null)
        {
            ThrowIfDisposed();
            
            var keyPair = RSAProvider.GenerateKeyPair(keySize);
            var keyPairInfo = new KeyPairInfo
            {
                Id = Guid.NewGuid(),
                Name = name,
                KeySize = keySize,
                Algorithm = "RSA",
                CreatedAt = DateTime.UtcNow,
                IsActive = true,
                Fingerprint = keyPair.GetFingerprint(),
                IsPasswordProtected = !string.IsNullOrEmpty(password)
            };

            // 保存密钥对到文件
            SaveKeyPairToFile(keyPairInfo, keyPair, password);
            
            // 缓存密钥对
            _keyCache[keyPairInfo.Id] = new CachedKeyPair
            {
                Info = keyPairInfo,
                KeyPair = keyPair,
                LoadedAt = DateTime.UtcNow
            };

            return keyPairInfo;
        }

        /// <summary>
        /// 导入现有的RSA密钥对
        /// </summary>
        /// <param name="name">密钥对名称</param>
        /// <param name="publicKeyPem">公钥PEM</param>
        /// <param name="privateKeyPem">私钥PEM</param>
        /// <param name="password">密码保护 (可选)</param>
        /// <returns>密钥对信息</returns>
        public KeyPairInfo ImportKeyPair(string name, string publicKeyPem, string privateKeyPem, string? password = null)
        {
            ThrowIfDisposed();
            
            // 验证密钥对
            using var rsa = new RSAProvider(privateKeyPem);
            var keySize = rsa.KeySize;
            
            var keyPair = new RSAKeyPair
            {
                PublicKey = publicKeyPem,
                PrivateKey = privateKeyPem,
                KeySize = keySize,
                CreatedAt = DateTime.UtcNow
            };

            var keyPairInfo = new KeyPairInfo
            {
                Id = Guid.NewGuid(),
                Name = name,
                KeySize = keySize,
                Algorithm = "RSA",
                CreatedAt = DateTime.UtcNow,
                IsActive = true,
                Fingerprint = keyPair.GetFingerprint(),
                IsPasswordProtected = !string.IsNullOrEmpty(password)
            };

            // 保存密钥对到文件
            SaveKeyPairToFile(keyPairInfo, keyPair, password);
            
            // 缓存密钥对
            _keyCache[keyPairInfo.Id] = new CachedKeyPair
            {
                Info = keyPairInfo,
                KeyPair = keyPair,
                LoadedAt = DateTime.UtcNow
            };

            return keyPairInfo;
        }

        /// <summary>
        /// 获取密钥对
        /// </summary>
        /// <param name="keyId">密钥ID</param>
        /// <param name="password">密码 (如果密钥受密码保护)</param>
        /// <returns>RSA密钥对</returns>
        public RSAKeyPair? GetKeyPair(Guid keyId, string? password = null)
        {
            ThrowIfDisposed();
            
            // 先检查缓存
            if (_keyCache.TryGetValue(keyId, out var cachedKeyPair))
            {
                return cachedKeyPair.KeyPair;
            }

            // 从文件加载
            var keyPairInfo = GetKeyPairInfo(keyId);
            if (keyPairInfo == null)
                return null;

            var keyPair = LoadKeyPairFromFile(keyPairInfo, password);
            if (keyPair != null)
            {
                // 缓存密钥对
                _keyCache[keyId] = new CachedKeyPair
                {
                    Info = keyPairInfo,
                    KeyPair = keyPair,
                    LoadedAt = DateTime.UtcNow
                };
            }

            return keyPair;
        }

        /// <summary>
        /// 获取密钥对信息
        /// </summary>
        /// <param name="keyId">密钥ID</param>
        /// <returns>密钥对信息</returns>
        public KeyPairInfo? GetKeyPairInfo(Guid keyId)
        {
            ThrowIfDisposed();
            
            // 先检查缓存
            if (_keyCache.TryGetValue(keyId, out var cachedKeyPair))
            {
                return cachedKeyPair.Info;
            }

            // 从文件加载信息
            var infoFilePath = Path.Combine(_keyStorePath, $"{keyId}.info");
            if (!File.Exists(infoFilePath))
                return null;

            try
            {
                var json = File.ReadAllText(infoFilePath);
                return JsonSerializer.Deserialize<KeyPairInfo>(json);
            }
            catch
            {
                return null;
            }
        }

        /// <summary>
        /// 列出所有密钥对信息
        /// </summary>
        /// <returns>密钥对信息列表</returns>
        public List<KeyPairInfo> ListKeyPairs()
        {
            ThrowIfDisposed();
            
            var keyPairs = new List<KeyPairInfo>();
            
            foreach (var infoFile in Directory.GetFiles(_keyStorePath, "*.info"))
            {
                try
                {
                    var json = File.ReadAllText(infoFile);
                    var keyPairInfo = JsonSerializer.Deserialize<KeyPairInfo>(json);
                    if (keyPairInfo != null)
                    {
                        keyPairs.Add(keyPairInfo);
                    }
                }
                catch
                {
                    // 忽略损坏的文件
                }
            }

            return keyPairs.OrderByDescending(k => k.CreatedAt).ToList();
        }

        /// <summary>
        /// 获取活跃的密钥对
        /// </summary>
        /// <returns>活跃的密钥对信息</returns>
        public KeyPairInfo? GetActiveKeyPair()
        {
            return ListKeyPairs().FirstOrDefault(k => k.IsActive);
        }

        /// <summary>
        /// 设置密钥对为活跃状态
        /// </summary>
        /// <param name="keyId">密钥ID</param>
        /// <returns>是否成功</returns>
        public bool SetActiveKeyPair(Guid keyId)
        {
            ThrowIfDisposed();
            
            var keyPairs = ListKeyPairs();
            var targetKeyPair = keyPairs.FirstOrDefault(k => k.Id == keyId);
            
            if (targetKeyPair == null)
                return false;

            // 将所有密钥设为非活跃
            foreach (var keyPair in keyPairs)
            {
                keyPair.IsActive = false;
                SaveKeyPairInfo(keyPair);
            }

            // 设置目标密钥为活跃
            targetKeyPair.IsActive = true;
            SaveKeyPairInfo(targetKeyPair);

            // 更新缓存
            if (_keyCache.TryGetValue(keyId, out var cachedKeyPair))
            {
                cachedKeyPair.Info.IsActive = true;
            }

            return true;
        }

        /// <summary>
        /// 删除密钥对
        /// </summary>
        /// <param name="keyId">密钥ID</param>
        /// <returns>是否成功</returns>
        public bool DeleteKeyPair(Guid keyId)
        {
            ThrowIfDisposed();
            
            try
            {
                var infoFilePath = Path.Combine(_keyStorePath, $"{keyId}.info");
                var keyFilePath = Path.Combine(_keyStorePath, $"{keyId}.key");

                if (File.Exists(infoFilePath))
                    File.Delete(infoFilePath);

                if (File.Exists(keyFilePath))
                    File.Delete(keyFilePath);

                // 从缓存中移除
                _keyCache.Remove(keyId);

                return true;
            }
            catch
            {
                return false;
            }
        }

        /// <summary>
        /// 更改密钥对密码
        /// </summary>
        /// <param name="keyId">密钥ID</param>
        /// <param name="oldPassword">旧密码</param>
        /// <param name="newPassword">新密码</param>
        /// <returns>是否成功</returns>
        public bool ChangeKeyPairPassword(Guid keyId, string? oldPassword, string? newPassword)
        {
            ThrowIfDisposed();
            
            var keyPairInfo = GetKeyPairInfo(keyId);
            if (keyPairInfo == null)
                return false;

            var keyPair = LoadKeyPairFromFile(keyPairInfo, oldPassword);
            if (keyPair == null)
                return false;

            // 更新密码保护状态
            keyPairInfo.IsPasswordProtected = !string.IsNullOrEmpty(newPassword);
            
            // 重新保存密钥对
            SaveKeyPairToFile(keyPairInfo, keyPair, newPassword);
            
            // 更新缓存
            if (_keyCache.TryGetValue(keyId, out var cachedKeyPair))
            {
                cachedKeyPair.Info.IsPasswordProtected = keyPairInfo.IsPasswordProtected;
            }

            return true;
        }

        /// <summary>
        /// 清理缓存
        /// </summary>
        public void ClearCache()
        {
            _keyCache.Clear();
        }

        private void SaveKeyPairToFile(KeyPairInfo keyPairInfo, RSAKeyPair keyPair, string? password)
        {
            // 保存密钥对信息
            SaveKeyPairInfo(keyPairInfo);

            // 保存密钥对数据
            var keyData = new
            {
                PublicKey = keyPair.PublicKey,
                PrivateKey = keyPair.PrivateKey
            };

            var json = JsonSerializer.Serialize(keyData, new JsonSerializerOptions { WriteIndented = true });
            
            // 如果有密码，则加密保存
            if (!string.IsNullOrEmpty(password))
            {
                var aesKey = AESProvider.DeriveKeyFromPassword(password);
                using var aes = new AESProvider(aesKey.Key, aesKey.IV);
                var encryptedJson = aes.EncryptStringWithIV(json);
                
                var encryptedData = new
                {
                    EncryptedData = encryptedJson,
                    KeyInfo = aesKey
                };
                
                json = JsonSerializer.Serialize(encryptedData, new JsonSerializerOptions { WriteIndented = true });
            }

            var keyFilePath = Path.Combine(_keyStorePath, $"{keyPairInfo.Id}.key");
            File.WriteAllText(keyFilePath, json);
        }

        private void SaveKeyPairInfo(KeyPairInfo keyPairInfo)
        {
            var infoFilePath = Path.Combine(_keyStorePath, $"{keyPairInfo.Id}.info");
            var json = JsonSerializer.Serialize(keyPairInfo, new JsonSerializerOptions { WriteIndented = true });
            File.WriteAllText(infoFilePath, json);
        }

        private RSAKeyPair? LoadKeyPairFromFile(KeyPairInfo keyPairInfo, string? password)
        {
            var keyFilePath = Path.Combine(_keyStorePath, $"{keyPairInfo.Id}.key");
            if (!File.Exists(keyFilePath))
                return null;

            try
            {
                var json = File.ReadAllText(keyFilePath);
                
                if (keyPairInfo.IsPasswordProtected)
                {
                    if (string.IsNullOrEmpty(password))
                        throw new UnauthorizedAccessException("Password required for encrypted key");

                    var encryptedData = JsonSerializer.Deserialize<JsonElement>(json);
                    var encryptedJson = encryptedData.GetProperty("EncryptedData").GetString()!;
                    var keyInfoElement = encryptedData.GetProperty("KeyInfo");
                    
                    var aesKeyInfo = JsonSerializer.Deserialize<AESKeyInfo>(keyInfoElement.GetRawText())!;
                    
                    // 从密码重新派生密钥
                    var derivedKey = AESProvider.DeriveKeyFromPassword(password, Convert.FromBase64String(aesKeyInfo.Salt), aesKeyInfo.KeySize, aesKeyInfo.Iterations);
                    
                    using var aes = new AESProvider(derivedKey.Key, derivedKey.IV);
                    json = aes.DecryptStringWithIV(encryptedJson);
                }

                var keyData = JsonSerializer.Deserialize<JsonElement>(json);
                var publicKey = keyData.GetProperty("PublicKey").GetString()!;
                var privateKey = keyData.GetProperty("PrivateKey").GetString()!;

                return new RSAKeyPair
                {
                    PublicKey = publicKey,
                    PrivateKey = privateKey,
                    KeySize = keyPairInfo.KeySize,
                    CreatedAt = keyPairInfo.CreatedAt
                };
            }
            catch
            {
                return null;
            }
        }

        private void ThrowIfDisposed()
        {
            if (_disposed)
                throw new ObjectDisposedException(nameof(KeyManager));
        }

        public void Dispose()
        {
            if (!_disposed)
            {
                ClearCache();
                _disposed = true;
            }
        }
    }

    /// <summary>
    /// 密钥对信息
    /// </summary>
    public class KeyPairInfo
    {
        /// <summary>
        /// 密钥对ID
        /// </summary>
        public Guid Id { get; set; }

        /// <summary>
        /// 密钥对名称
        /// </summary>
        public string Name { get; set; } = string.Empty;

        /// <summary>
        /// 密钥长度
        /// </summary>
        public int KeySize { get; set; }

        /// <summary>
        /// 算法类型
        /// </summary>
        public string Algorithm { get; set; } = string.Empty;

        /// <summary>
        /// 创建时间
        /// </summary>
        public DateTime CreatedAt { get; set; }

        /// <summary>
        /// 是否为活跃密钥
        /// </summary>
        public bool IsActive { get; set; }

        /// <summary>
        /// 公钥指纹
        /// </summary>
        public string Fingerprint { get; set; } = string.Empty;

        /// <summary>
        /// 是否受密码保护
        /// </summary>
        public bool IsPasswordProtected { get; set; }

        /// <summary>
        /// 描述
        /// </summary>
        public string Description { get; set; } = string.Empty;
    }

    /// <summary>
    /// 缓存的密钥对
    /// </summary>
    internal class CachedKeyPair
    {
        public KeyPairInfo Info { get; set; } = null!;
        public RSAKeyPair KeyPair { get; set; } = null!;
        public DateTime LoadedAt { get; set; }
    }
}
