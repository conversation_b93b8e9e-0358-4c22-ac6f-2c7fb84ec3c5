using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;

namespace LicenseGenerator.Core.Models
{
    /// <summary>
    /// 设备信息模型 - 用于设备指纹识别
    /// </summary>
    public class DeviceInfo
    {
        /// <summary>
        /// 设备信息唯一标识符
        /// </summary>
        public Guid Id { get; set; } = Guid.NewGuid();

        /// <summary>
        /// 设备唯一标识符 (基于硬件信息生成的哈希值)
        /// </summary>
        [Required]
        [StringLength(64)]
        public string DeviceId { get; set; } = string.Empty;

        /// <summary>
        /// CPU标识符
        /// </summary>
        [StringLength(100)]
        public string CpuId { get; set; } = string.Empty;

        /// <summary>
        /// CPU名称
        /// </summary>
        [StringLength(200)]
        public string CpuName { get; set; } = string.Empty;

        /// <summary>
        /// 主板序列号
        /// </summary>
        [StringLength(100)]
        public string MotherboardSerial { get; set; } = string.Empty;

        /// <summary>
        /// 主板制造商
        /// </summary>
        [StringLength(100)]
        public string MotherboardManufacturer { get; set; } = string.Empty;

        /// <summary>
        /// MAC地址列表
        /// </summary>
        public List<string> MacAddresses { get; set; } = new List<string>();

        /// <summary>
        /// 硬盘序列号列表
        /// </summary>
        public List<string> HardDiskSerials { get; set; } = new List<string>();

        /// <summary>
        /// 内存信息
        /// </summary>
        public MemoryInfo Memory { get; set; } = new MemoryInfo();

        /// <summary>
        /// 操作系统信息
        /// </summary>
        public OperatingSystemInfo OperatingSystem { get; set; } = new OperatingSystemInfo();

        /// <summary>
        /// BIOS信息
        /// </summary>
        public BiosInfo Bios { get; set; } = new BiosInfo();

        /// <summary>
        /// 设备指纹收集时间
        /// </summary>
        public DateTime CollectedAt { get; set; } = DateTime.UtcNow;

        /// <summary>
        /// 设备指纹版本 (用于兼容性管理)
        /// </summary>
        public int FingerprintVersion { get; set; } = 1;

        /// <summary>
        /// 原始指纹数据 (JSON格式)
        /// </summary>
        public string RawFingerprintData { get; set; } = string.Empty;

        /// <summary>
        /// 设备信任级别
        /// </summary>
        public DeviceTrustLevel TrustLevel { get; set; } = DeviceTrustLevel.Unknown;

        /// <summary>
        /// 备注信息
        /// </summary>
        [StringLength(1000)]
        public string Notes { get; set; } = string.Empty;

        /// <summary>
        /// 获取主要MAC地址 (第一个非虚拟网卡的MAC地址)
        /// </summary>
        public string PrimaryMacAddress => MacAddresses.FirstOrDefault() ?? string.Empty;

        /// <summary>
        /// 获取主要硬盘序列号
        /// </summary>
        public string PrimaryHardDiskSerial => HardDiskSerials.FirstOrDefault() ?? string.Empty;

        /// <summary>
        /// 检查设备指纹是否完整
        /// </summary>
        public bool IsComplete => !string.IsNullOrWhiteSpace(CpuId) && 
                                  !string.IsNullOrWhiteSpace(MotherboardSerial) &&
                                  MacAddresses.Any() &&
                                  HardDiskSerials.Any();

        /// <summary>
        /// 获取设备摘要信息
        /// </summary>
        public string DeviceSummary
        {
            get
            {
                var parts = new[]
                {
                    !string.IsNullOrWhiteSpace(CpuName) ? $"CPU: {CpuName}" : null,
                    !string.IsNullOrWhiteSpace(MotherboardManufacturer) ? $"主板: {MotherboardManufacturer}" : null,
                    OperatingSystem.IsValid ? $"OS: {OperatingSystem.Name} {OperatingSystem.Version}" : null
                };
                return string.Join(" | ", parts.Where(p => !string.IsNullOrWhiteSpace(p)));
            }
        }
    }

    /// <summary>
    /// 内存信息
    /// </summary>
    public class MemoryInfo
    {
        /// <summary>
        /// 总内存大小 (MB)
        /// </summary>
        public long TotalMemoryMB { get; set; }

        /// <summary>
        /// 内存条数量
        /// </summary>
        public int MemorySlots { get; set; }

        /// <summary>
        /// 内存制造商信息
        /// </summary>
        public List<string> Manufacturers { get; set; } = new List<string>();
    }

    /// <summary>
    /// 操作系统信息
    /// </summary>
    public class OperatingSystemInfo
    {
        /// <summary>
        /// 操作系统名称
        /// </summary>
        public string Name { get; set; } = string.Empty;

        /// <summary>
        /// 操作系统版本
        /// </summary>
        public string Version { get; set; } = string.Empty;

        /// <summary>
        /// 系统架构 (x86, x64, ARM等)
        /// </summary>
        public string Architecture { get; set; } = string.Empty;

        /// <summary>
        /// 系统安装日期
        /// </summary>
        public DateTime? InstallDate { get; set; }

        /// <summary>
        /// 检查操作系统信息是否有效
        /// </summary>
        public bool IsValid => !string.IsNullOrWhiteSpace(Name) && !string.IsNullOrWhiteSpace(Version);
    }

    /// <summary>
    /// BIOS信息
    /// </summary>
    public class BiosInfo
    {
        /// <summary>
        /// BIOS制造商
        /// </summary>
        public string Manufacturer { get; set; } = string.Empty;

        /// <summary>
        /// BIOS版本
        /// </summary>
        public string Version { get; set; } = string.Empty;

        /// <summary>
        /// BIOS序列号
        /// </summary>
        public string SerialNumber { get; set; } = string.Empty;

        /// <summary>
        /// BIOS发布日期
        /// </summary>
        public DateTime? ReleaseDate { get; set; }
    }

    /// <summary>
    /// 设备信任级别枚举
    /// </summary>
    public enum DeviceTrustLevel
    {
        /// <summary>
        /// 未知
        /// </summary>
        Unknown = 0,

        /// <summary>
        /// 低信任度 (信息不完整或可疑)
        /// </summary>
        Low = 1,

        /// <summary>
        /// 中等信任度 (基本信息完整)
        /// </summary>
        Medium = 2,

        /// <summary>
        /// 高信任度 (信息完整且一致)
        /// </summary>
        High = 3,

        /// <summary>
        /// 已验证 (经过人工验证)
        /// </summary>
        Verified = 4
    }
}
