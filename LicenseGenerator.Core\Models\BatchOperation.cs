using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;

namespace LicenseGenerator.Core.Models
{
    /// <summary>
    /// 批量操作模型
    /// </summary>
    public class BatchOperation
    {
        /// <summary>
        /// 批量操作唯一标识符
        /// </summary>
        public Guid Id { get; set; } = Guid.NewGuid();

        /// <summary>
        /// 操作名称
        /// </summary>
        [Required]
        [StringLength(200)]
        public string Name { get; set; } = string.Empty;

        /// <summary>
        /// 操作类型
        /// </summary>
        public BatchOperationType OperationType { get; set; } = BatchOperationType.LicenseGeneration;

        /// <summary>
        /// 操作状态
        /// </summary>
        public BatchOperationStatus Status { get; set; } = BatchOperationStatus.Pending;

        /// <summary>
        /// 总项目数
        /// </summary>
        public int TotalItems { get; set; } = 0;

        /// <summary>
        /// 已处理项目数
        /// </summary>
        public int ProcessedItems { get; set; } = 0;

        /// <summary>
        /// 成功处理项目数
        /// </summary>
        public int SuccessfulItems { get; set; } = 0;

        /// <summary>
        /// 失败项目数
        /// </summary>
        public int FailedItems { get; set; } = 0;

        /// <summary>
        /// 操作开始时间
        /// </summary>
        public DateTime? StartTime { get; set; }

        /// <summary>
        /// 操作结束时间
        /// </summary>
        public DateTime? EndTime { get; set; }

        /// <summary>
        /// 创建时间
        /// </summary>
        public DateTime CreatedAt { get; set; } = DateTime.UtcNow;

        /// <summary>
        /// 创建者
        /// </summary>
        [StringLength(100)]
        public string CreatedBy { get; set; } = string.Empty;

        /// <summary>
        /// 输入文件路径
        /// </summary>
        [StringLength(500)]
        public string InputFilePath { get; set; } = string.Empty;

        /// <summary>
        /// 输出目录路径
        /// </summary>
        [StringLength(500)]
        public string OutputDirectoryPath { get; set; } = string.Empty;

        /// <summary>
        /// 操作配置 (JSON格式)
        /// </summary>
        public string Configuration { get; set; } = string.Empty;

        /// <summary>
        /// 错误信息
        /// </summary>
        public string ErrorMessage { get; set; } = string.Empty;

        /// <summary>
        /// 操作日志
        /// </summary>
        public List<BatchOperationLog> Logs { get; set; } = new List<BatchOperationLog>();

        /// <summary>
        /// 操作结果详情
        /// </summary>
        public List<BatchOperationResult> Results { get; set; } = new List<BatchOperationResult>();

        /// <summary>
        /// 备注信息
        /// </summary>
        [StringLength(1000)]
        public string Notes { get; set; } = string.Empty;

        /// <summary>
        /// 计算操作进度百分比
        /// </summary>
        public double ProgressPercentage
        {
            get
            {
                if (TotalItems == 0) return 0;
                return (double)ProcessedItems / TotalItems * 100;
            }
        }

        /// <summary>
        /// 计算操作耗时
        /// </summary>
        public TimeSpan? Duration
        {
            get
            {
                if (!StartTime.HasValue) return null;
                var endTime = EndTime ?? DateTime.UtcNow;
                return endTime - StartTime.Value;
            }
        }

        /// <summary>
        /// 检查操作是否已完成
        /// </summary>
        public bool IsCompleted => Status == BatchOperationStatus.Completed || 
                                   Status == BatchOperationStatus.Failed ||
                                   Status == BatchOperationStatus.Cancelled;

        /// <summary>
        /// 获取成功率
        /// </summary>
        public double SuccessRate
        {
            get
            {
                if (ProcessedItems == 0) return 0;
                return (double)SuccessfulItems / ProcessedItems * 100;
            }
        }
    }

    /// <summary>
    /// 批量操作日志
    /// </summary>
    public class BatchOperationLog
    {
        /// <summary>
        /// 日志唯一标识符
        /// </summary>
        public Guid Id { get; set; } = Guid.NewGuid();

        /// <summary>
        /// 批量操作ID
        /// </summary>
        public Guid BatchOperationId { get; set; }

        /// <summary>
        /// 日志时间
        /// </summary>
        public DateTime Timestamp { get; set; } = DateTime.UtcNow;

        /// <summary>
        /// 日志级别
        /// </summary>
        public LogLevel Level { get; set; } = LogLevel.Information;

        /// <summary>
        /// 日志消息
        /// </summary>
        [Required]
        public string Message { get; set; } = string.Empty;

        /// <summary>
        /// 详细信息
        /// </summary>
        public string Details { get; set; } = string.Empty;

        /// <summary>
        /// 相关项目索引
        /// </summary>
        public int? ItemIndex { get; set; }
    }

    /// <summary>
    /// 批量操作结果
    /// </summary>
    public class BatchOperationResult
    {
        /// <summary>
        /// 结果唯一标识符
        /// </summary>
        public Guid Id { get; set; } = Guid.NewGuid();

        /// <summary>
        /// 批量操作ID
        /// </summary>
        public Guid BatchOperationId { get; set; }

        /// <summary>
        /// 项目索引
        /// </summary>
        public int ItemIndex { get; set; }

        /// <summary>
        /// 项目标识符
        /// </summary>
        [StringLength(200)]
        public string ItemIdentifier { get; set; } = string.Empty;

        /// <summary>
        /// 处理状态
        /// </summary>
        public ProcessingStatus Status { get; set; } = ProcessingStatus.Pending;

        /// <summary>
        /// 输出文件路径
        /// </summary>
        [StringLength(500)]
        public string OutputFilePath { get; set; } = string.Empty;

        /// <summary>
        /// 错误信息
        /// </summary>
        public string ErrorMessage { get; set; } = string.Empty;

        /// <summary>
        /// 处理时间
        /// </summary>
        public DateTime? ProcessedAt { get; set; }

        /// <summary>
        /// 处理耗时 (毫秒)
        /// </summary>
        public long ProcessingTimeMs { get; set; }

        /// <summary>
        /// 附加数据 (JSON格式)
        /// </summary>
        public string AdditionalData { get; set; } = string.Empty;
    }

    /// <summary>
    /// 批量操作类型枚举
    /// </summary>
    public enum BatchOperationType
    {
        /// <summary>
        /// License生成
        /// </summary>
        LicenseGeneration = 0,

        /// <summary>
        /// License验证
        /// </summary>
        LicenseValidation = 1,

        /// <summary>
        /// License撤销
        /// </summary>
        LicenseRevocation = 2,

        /// <summary>
        /// 设备指纹导入
        /// </summary>
        DeviceFingerprintImport = 3,

        /// <summary>
        /// 数据导出
        /// </summary>
        DataExport = 4
    }

    /// <summary>
    /// 批量操作状态枚举
    /// </summary>
    public enum BatchOperationStatus
    {
        /// <summary>
        /// 等待中
        /// </summary>
        Pending = 0,

        /// <summary>
        /// 运行中
        /// </summary>
        Running = 1,

        /// <summary>
        /// 已暂停
        /// </summary>
        Paused = 2,

        /// <summary>
        /// 已完成
        /// </summary>
        Completed = 3,

        /// <summary>
        /// 已失败
        /// </summary>
        Failed = 4,

        /// <summary>
        /// 已取消
        /// </summary>
        Cancelled = 5
    }

    /// <summary>
    /// 处理状态枚举
    /// </summary>
    public enum ProcessingStatus
    {
        /// <summary>
        /// 等待处理
        /// </summary>
        Pending = 0,

        /// <summary>
        /// 处理中
        /// </summary>
        Processing = 1,

        /// <summary>
        /// 处理成功
        /// </summary>
        Success = 2,

        /// <summary>
        /// 处理失败
        /// </summary>
        Failed = 3,

        /// <summary>
        /// 已跳过
        /// </summary>
        Skipped = 4
    }

    /// <summary>
    /// 日志级别枚举
    /// </summary>
    public enum LogLevel
    {
        /// <summary>
        /// 调试信息
        /// </summary>
        Debug = 0,

        /// <summary>
        /// 一般信息
        /// </summary>
        Information = 1,

        /// <summary>
        /// 警告信息
        /// </summary>
        Warning = 2,

        /// <summary>
        /// 错误信息
        /// </summary>
        Error = 3,

        /// <summary>
        /// 严重错误
        /// </summary>
        Critical = 4
    }
}
