using System;
using System.IO;
using System.Security.Cryptography;
using System.Text;

namespace LicenseGenerator.Crypto
{
    /// <summary>
    /// AES加密提供者 - 负责AES对称加密和解密
    /// </summary>
    public class AESProvider : IDisposable
    {
        private Aes? _aes;
        private bool _disposed = false;

        /// <summary>
        /// 初始化AES提供者
        /// </summary>
        /// <param name="keySize">密钥长度，默认256位</param>
        public AESProvider(int keySize = 256)
        {
            _aes = Aes.Create();
            _aes.KeySize = keySize;
            _aes.Mode = CipherMode.CBC;
            _aes.Padding = PaddingMode.PKCS7;
            _aes.GenerateKey();
            _aes.GenerateIV();
        }

        /// <summary>
        /// 使用指定密钥和IV初始化AES提供者
        /// </summary>
        /// <param name="key">AES密钥</param>
        /// <param name="iv">初始化向量</param>
        public AESProvider(byte[] key, byte[] iv)
        {
            _aes = Aes.Create();
            _aes.Key = key;
            _aes.IV = iv;
            _aes.Mode = CipherMode.CBC;
            _aes.Padding = PaddingMode.PKCS7;
        }

        /// <summary>
        /// 使用Base64编码的密钥和IV初始化AES提供者
        /// </summary>
        /// <param name="keyBase64">Base64编码的AES密钥</param>
        /// <param name="ivBase64">Base64编码的初始化向量</param>
        public AESProvider(string keyBase64, string ivBase64)
        {
            _aes = Aes.Create();
            _aes.Key = Convert.FromBase64String(keyBase64);
            _aes.IV = Convert.FromBase64String(ivBase64);
            _aes.Mode = CipherMode.CBC;
            _aes.Padding = PaddingMode.PKCS7;
        }

        /// <summary>
        /// 生成随机AES密钥
        /// </summary>
        /// <param name="keySize">密钥长度</param>
        /// <returns>AES密钥信息</returns>
        public static AESKeyInfo GenerateKey(int keySize = 256)
        {
            using var aes = Aes.Create();
            aes.KeySize = keySize;
            aes.GenerateKey();
            aes.GenerateIV();

            return new AESKeyInfo
            {
                Key = Convert.ToBase64String(aes.Key),
                IV = Convert.ToBase64String(aes.IV),
                KeySize = keySize,
                CreatedAt = DateTime.UtcNow
            };
        }

        /// <summary>
        /// 从密码派生AES密钥
        /// </summary>
        /// <param name="password">密码</param>
        /// <param name="salt">盐值</param>
        /// <param name="keySize">密钥长度</param>
        /// <param name="iterations">迭代次数</param>
        /// <returns>AES密钥信息</returns>
        public static AESKeyInfo DeriveKeyFromPassword(string password, byte[] salt, int keySize = 256, int iterations = 10000)
        {
            using var pbkdf2 = new Rfc2898DeriveBytes(password, salt, iterations, HashAlgorithmName.SHA256);
            
            var key = pbkdf2.GetBytes(keySize / 8);
            var iv = pbkdf2.GetBytes(16); // AES块大小固定为128位(16字节)

            return new AESKeyInfo
            {
                Key = Convert.ToBase64String(key),
                IV = Convert.ToBase64String(iv),
                KeySize = keySize,
                Salt = Convert.ToBase64String(salt),
                Iterations = iterations,
                CreatedAt = DateTime.UtcNow
            };
        }

        /// <summary>
        /// 从密码派生AES密钥 (自动生成盐值)
        /// </summary>
        /// <param name="password">密码</param>
        /// <param name="keySize">密钥长度</param>
        /// <param name="iterations">迭代次数</param>
        /// <returns>AES密钥信息</returns>
        public static AESKeyInfo DeriveKeyFromPassword(string password, int keySize = 256, int iterations = 10000)
        {
            var salt = new byte[32]; // 256位盐值
            using var rng = RandomNumberGenerator.Create();
            rng.GetBytes(salt);

            return DeriveKeyFromPassword(password, salt, keySize, iterations);
        }

        /// <summary>
        /// AES加密数据
        /// </summary>
        /// <param name="data">要加密的数据</param>
        /// <returns>加密后的数据</returns>
        public byte[] Encrypt(byte[] data)
        {
            ThrowIfDisposed();
            
            using var encryptor = _aes!.CreateEncryptor();
            using var msEncrypt = new MemoryStream();
            using var csEncrypt = new CryptoStream(msEncrypt, encryptor, CryptoStreamMode.Write);
            
            csEncrypt.Write(data, 0, data.Length);
            csEncrypt.FlushFinalBlock();
            
            return msEncrypt.ToArray();
        }

        /// <summary>
        /// AES加密字符串
        /// </summary>
        /// <param name="plainText">要加密的字符串</param>
        /// <returns>Base64编码的加密数据</returns>
        public string EncryptString(string plainText)
        {
            var data = Encoding.UTF8.GetBytes(plainText);
            var encryptedData = Encrypt(data);
            return Convert.ToBase64String(encryptedData);
        }

        /// <summary>
        /// AES解密数据
        /// </summary>
        /// <param name="encryptedData">加密的数据</param>
        /// <returns>解密后的数据</returns>
        public byte[] Decrypt(byte[] encryptedData)
        {
            ThrowIfDisposed();
            
            using var decryptor = _aes!.CreateDecryptor();
            using var msDecrypt = new MemoryStream(encryptedData);
            using var csDecrypt = new CryptoStream(msDecrypt, decryptor, CryptoStreamMode.Read);
            using var msResult = new MemoryStream();
            
            csDecrypt.CopyTo(msResult);
            return msResult.ToArray();
        }

        /// <summary>
        /// AES解密字符串
        /// </summary>
        /// <param name="encryptedText">Base64编码的加密数据</param>
        /// <returns>解密后的字符串</returns>
        public string DecryptString(string encryptedText)
        {
            var encryptedData = Convert.FromBase64String(encryptedText);
            var data = Decrypt(encryptedData);
            return Encoding.UTF8.GetString(data);
        }

        /// <summary>
        /// 加密数据并包含IV (适用于每次加密使用不同IV的场景)
        /// </summary>
        /// <param name="data">要加密的数据</param>
        /// <returns>包含IV的加密数据</returns>
        public byte[] EncryptWithIV(byte[] data)
        {
            ThrowIfDisposed();
            
            // 生成新的IV
            _aes!.GenerateIV();
            
            var encryptedData = Encrypt(data);
            
            // 将IV和加密数据合并
            var result = new byte[_aes.IV.Length + encryptedData.Length];
            Array.Copy(_aes.IV, 0, result, 0, _aes.IV.Length);
            Array.Copy(encryptedData, 0, result, _aes.IV.Length, encryptedData.Length);
            
            return result;
        }

        /// <summary>
        /// 解密包含IV的数据
        /// </summary>
        /// <param name="dataWithIV">包含IV的加密数据</param>
        /// <returns>解密后的数据</returns>
        public byte[] DecryptWithIV(byte[] dataWithIV)
        {
            ThrowIfDisposed();
            
            // 提取IV
            var iv = new byte[16]; // AES IV固定16字节
            Array.Copy(dataWithIV, 0, iv, 0, iv.Length);
            
            // 提取加密数据
            var encryptedData = new byte[dataWithIV.Length - iv.Length];
            Array.Copy(dataWithIV, iv.Length, encryptedData, 0, encryptedData.Length);
            
            // 设置IV并解密
            _aes!.IV = iv;
            return Decrypt(encryptedData);
        }

        /// <summary>
        /// 加密字符串并包含IV
        /// </summary>
        /// <param name="plainText">要加密的字符串</param>
        /// <returns>Base64编码的包含IV的加密数据</returns>
        public string EncryptStringWithIV(string plainText)
        {
            var data = Encoding.UTF8.GetBytes(plainText);
            var encryptedData = EncryptWithIV(data);
            return Convert.ToBase64String(encryptedData);
        }

        /// <summary>
        /// 解密包含IV的字符串
        /// </summary>
        /// <param name="encryptedText">Base64编码的包含IV的加密数据</param>
        /// <returns>解密后的字符串</returns>
        public string DecryptStringWithIV(string encryptedText)
        {
            var dataWithIV = Convert.FromBase64String(encryptedText);
            var data = DecryptWithIV(dataWithIV);
            return Encoding.UTF8.GetString(data);
        }

        /// <summary>
        /// 获取当前密钥 (Base64编码)
        /// </summary>
        public string GetKeyBase64()
        {
            ThrowIfDisposed();
            return Convert.ToBase64String(_aes!.Key);
        }

        /// <summary>
        /// 获取当前IV (Base64编码)
        /// </summary>
        public string GetIVBase64()
        {
            ThrowIfDisposed();
            return Convert.ToBase64String(_aes!.IV);
        }

        /// <summary>
        /// 获取密钥大小
        /// </summary>
        public int KeySize => _aes?.KeySize ?? 0;

        private void ThrowIfDisposed()
        {
            if (_disposed)
                throw new ObjectDisposedException(nameof(AESProvider));
        }

        public void Dispose()
        {
            if (!_disposed)
            {
                _aes?.Dispose();
                _aes = null;
                _disposed = true;
            }
        }
    }

    /// <summary>
    /// AES密钥信息
    /// </summary>
    public class AESKeyInfo
    {
        /// <summary>
        /// AES密钥 (Base64编码)
        /// </summary>
        public string Key { get; set; } = string.Empty;

        /// <summary>
        /// 初始化向量 (Base64编码)
        /// </summary>
        public string IV { get; set; } = string.Empty;

        /// <summary>
        /// 密钥长度
        /// </summary>
        public int KeySize { get; set; }

        /// <summary>
        /// 盐值 (Base64编码，用于密码派生)
        /// </summary>
        public string Salt { get; set; } = string.Empty;

        /// <summary>
        /// 迭代次数 (用于密码派生)
        /// </summary>
        public int Iterations { get; set; }

        /// <summary>
        /// 创建时间
        /// </summary>
        public DateTime CreatedAt { get; set; }
    }
}
