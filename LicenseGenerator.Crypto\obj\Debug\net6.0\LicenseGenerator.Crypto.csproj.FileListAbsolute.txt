D:\00 Crack\LicenseGenerator.Crypto\bin\Debug\net6.0\LicenseGenerator.Crypto.deps.json
D:\00 Crack\LicenseGenerator.Crypto\bin\Debug\net6.0\LicenseGenerator.Crypto.dll
D:\00 Crack\LicenseGenerator.Crypto\bin\Debug\net6.0\LicenseGenerator.Crypto.pdb
D:\00 Crack\LicenseGenerator.Crypto\obj\Debug\net6.0\LicenseGenerator.Crypto.GeneratedMSBuildEditorConfig.editorconfig
D:\00 Crack\LicenseGenerator.Crypto\obj\Debug\net6.0\LicenseGenerator.Crypto.AssemblyInfoInputs.cache
D:\00 Crack\LicenseGenerator.Crypto\obj\Debug\net6.0\LicenseGenerator.Crypto.AssemblyInfo.cs
D:\00 Crack\LicenseGenerator.Crypto\obj\Debug\net6.0\LicenseGenerator.Crypto.csproj.CoreCompileInputs.cache
D:\00 Crack\LicenseGenerator.Crypto\obj\Debug\net6.0\LicenseGenerator.Crypto.dll
D:\00 Crack\LicenseGenerator.Crypto\obj\Debug\net6.0\refint\LicenseGenerator.Crypto.dll
D:\00 Crack\LicenseGenerator.Crypto\obj\Debug\net6.0\LicenseGenerator.Crypto.pdb
D:\00 Crack\LicenseGenerator.Crypto\obj\Debug\net6.0\ref\LicenseGenerator.Crypto.dll
