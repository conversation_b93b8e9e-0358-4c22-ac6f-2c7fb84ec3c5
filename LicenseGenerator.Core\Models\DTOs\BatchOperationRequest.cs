using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;

namespace LicenseGenerator.Core.Models.DTOs
{
    /// <summary>
    /// 批量操作请求模型
    /// </summary>
    public class BatchOperationRequest
    {
        /// <summary>
        /// 操作名称
        /// </summary>
        [Required]
        [StringLength(200)]
        public string Name { get; set; } = string.Empty;

        /// <summary>
        /// 操作类型
        /// </summary>
        public BatchOperationType OperationType { get; set; } = BatchOperationType.LicenseGeneration;

        /// <summary>
        /// 输入文件路径 (Excel/CSV文件)
        /// </summary>
        [Required]
        [StringLength(500)]
        public string InputFilePath { get; set; } = string.Empty;

        /// <summary>
        /// 输出目录路径
        /// </summary>
        [Required]
        [StringLength(500)]
        public string OutputDirectoryPath { get; set; } = string.Empty;

        /// <summary>
        /// 批量操作配置
        /// </summary>
        public BatchOperationConfig Configuration { get; set; } = new BatchOperationConfig();

        /// <summary>
        /// 创建者
        /// </summary>
        [StringLength(100)]
        public string CreatedBy { get; set; } = string.Empty;

        /// <summary>
        /// 备注信息
        /// </summary>
        [StringLength(1000)]
        public string Notes { get; set; } = string.Empty;
    }

    /// <summary>
    /// 批量操作配置
    /// </summary>
    public class BatchOperationConfig
    {
        /// <summary>
        /// 应用程序标识符 (用于批量License生成)
        /// </summary>
        [StringLength(100)]
        public string AppId { get; set; } = string.Empty;

        /// <summary>
        /// 默认License类型
        /// </summary>
        public LicenseType DefaultLicenseType { get; set; } = LicenseType.Trial;

        /// <summary>
        /// 默认有效期天数
        /// </summary>
        public int? DefaultValidityDays { get; set; }

        /// <summary>
        /// 默认授权功能列表
        /// </summary>
        public List<string> DefaultFeatures { get; set; } = new List<string>();

        /// <summary>
        /// 使用的密钥ID
        /// </summary>
        public Guid? KeyId { get; set; }

        /// <summary>
        /// 文件名模板 (支持变量替换)
        /// </summary>
        [StringLength(200)]
        public string FileNameTemplate { get; set; } = "{CustomerName}_{AppId}_License.lic";

        /// <summary>
        /// 是否在出错时继续处理
        /// </summary>
        public bool ContinueOnError { get; set; } = true;

        /// <summary>
        /// 最大并行处理数
        /// </summary>
        public int MaxParallelism { get; set; } = Environment.ProcessorCount;

        /// <summary>
        /// 输入文件格式
        /// </summary>
        public InputFileFormat InputFormat { get; set; } = InputFileFormat.Excel;

        /// <summary>
        /// Excel/CSV列映射配置
        /// </summary>
        public Dictionary<string, string> ColumnMappings { get; set; } = new Dictionary<string, string>();

        /// <summary>
        /// 是否跳过标题行
        /// </summary>
        public bool SkipHeaderRow { get; set; } = true;

        /// <summary>
        /// CSV分隔符 (仅当InputFormat为CSV时有效)
        /// </summary>
        public char CsvDelimiter { get; set; } = ',';
    }

    /// <summary>
    /// 批量操作响应模型
    /// </summary>
    public class BatchOperationResponse
    {
        /// <summary>
        /// 是否成功启动
        /// </summary>
        public bool Success { get; set; }

        /// <summary>
        /// 批量操作信息
        /// </summary>
        public BatchOperation? BatchOperation { get; set; }

        /// <summary>
        /// 错误信息
        /// </summary>
        public string ErrorMessage { get; set; } = string.Empty;

        /// <summary>
        /// 预估处理项目数
        /// </summary>
        public int EstimatedItemCount { get; set; }

        /// <summary>
        /// 启动时间
        /// </summary>
        public DateTime StartedAt { get; set; } = DateTime.UtcNow;
    }

    /// <summary>
    /// 批量操作状态查询响应
    /// </summary>
    public class BatchOperationStatusResponse
    {
        /// <summary>
        /// 批量操作信息
        /// </summary>
        public BatchOperation? BatchOperation { get; set; }

        /// <summary>
        /// 最新日志条目
        /// </summary>
        public List<BatchOperationLog> RecentLogs { get; set; } = new List<BatchOperationLog>();

        /// <summary>
        /// 最新结果条目
        /// </summary>
        public List<BatchOperationResult> RecentResults { get; set; } = new List<BatchOperationResult>();

        /// <summary>
        /// 查询时间
        /// </summary>
        public DateTime QueryTime { get; set; } = DateTime.UtcNow;
    }

    /// <summary>
    /// 批量导入数据项
    /// </summary>
    public class BatchImportItem
    {
        /// <summary>
        /// 行号
        /// </summary>
        public int RowNumber { get; set; }

        /// <summary>
        /// 客户信息
        /// </summary>
        public CustomerInfo Customer { get; set; } = new CustomerInfo();

        /// <summary>
        /// 设备指纹
        /// </summary>
        public string DeviceFingerprint { get; set; } = string.Empty;

        /// <summary>
        /// License类型 (可选，使用默认值如果未指定)
        /// </summary>
        public LicenseType? LicenseType { get; set; }

        /// <summary>
        /// 有效期天数 (可选，使用默认值如果未指定)
        /// </summary>
        public int? ValidityDays { get; set; }

        /// <summary>
        /// 授权功能列表 (可选，使用默认值如果未指定)
        /// </summary>
        public List<string> Features { get; set; } = new List<string>();

        /// <summary>
        /// 最大并发用户数
        /// </summary>
        public int? MaxConcurrentUsers { get; set; }

        /// <summary>
        /// 备注信息
        /// </summary>
        public string Notes { get; set; } = string.Empty;

        /// <summary>
        /// 原始数据 (用于调试和错误追踪)
        /// </summary>
        public Dictionary<string, object> RawData { get; set; } = new Dictionary<string, object>();
    }

    /// <summary>
    /// 输入文件格式枚举
    /// </summary>
    public enum InputFileFormat
    {
        /// <summary>
        /// Excel文件 (.xlsx, .xls)
        /// </summary>
        Excel = 0,

        /// <summary>
        /// CSV文件
        /// </summary>
        CSV = 1,

        /// <summary>
        /// JSON文件
        /// </summary>
        JSON = 2,

        /// <summary>
        /// XML文件
        /// </summary>
        XML = 3
    }
}
