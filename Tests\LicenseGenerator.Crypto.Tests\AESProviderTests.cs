using System;
using System.Text;
using LicenseGenerator.Crypto;
using Xunit;

namespace LicenseGenerator.Crypto.Tests
{
    /// <summary>
    /// AES提供者测试
    /// </summary>
    public class AESProviderTests : IDisposable
    {
        private AESProvider? _aesProvider;

        public AESProviderTests()
        {
            _aesProvider = new AESProvider(256);
        }

        [Fact]
        public void GenerateKey_ShouldCreateValidKey()
        {
            // Act
            var keyInfo = AESProvider.GenerateKey(256);

            // Assert
            Assert.NotNull(keyInfo);
            Assert.NotEmpty(keyInfo.Key);
            Assert.NotEmpty(keyInfo.IV);
            Assert.Equal(256, keyInfo.KeySize);
            Assert.True(keyInfo.CreatedAt <= DateTime.UtcNow);
        }

        [Fact]
        public void EncryptDecrypt_ShouldWorkCorrectly()
        {
            // Arrange
            var plainText = "Hello, World! This is a test message for AES encryption. It can be quite long and contain various characters: 中文, émojis 🚀, and symbols @#$%^&*()!";

            // Act
            var encryptedText = _aesProvider!.EncryptString(plainText);
            var decryptedText = _aesProvider.DecryptString(encryptedText);

            // Assert
            Assert.NotEqual(plainText, encryptedText);
            Assert.Equal(plainText, decryptedText);
        }

        [Fact]
        public void EncryptDecryptWithIV_ShouldWorkCorrectly()
        {
            // Arrange
            var plainText = "Test message for AES encryption with IV";

            // Act
            var encryptedText = _aesProvider!.EncryptStringWithIV(plainText);
            var decryptedText = _aesProvider.DecryptStringWithIV(encryptedText);

            // Assert
            Assert.NotEqual(plainText, encryptedText);
            Assert.Equal(plainText, decryptedText);
        }

        [Fact]
        public void EncryptWithIV_ShouldProduceDifferentResults()
        {
            // Arrange
            var plainText = "Same message";

            // Act
            var encrypted1 = _aesProvider!.EncryptStringWithIV(plainText);
            var encrypted2 = _aesProvider.EncryptStringWithIV(plainText);

            // Assert
            Assert.NotEqual(encrypted1, encrypted2); // Different IVs should produce different results
            
            // But both should decrypt to the same plaintext
            var decrypted1 = _aesProvider.DecryptStringWithIV(encrypted1);
            var decrypted2 = _aesProvider.DecryptStringWithIV(encrypted2);
            Assert.Equal(plainText, decrypted1);
            Assert.Equal(plainText, decrypted2);
        }

        [Fact]
        public void DeriveKeyFromPassword_ShouldCreateValidKey()
        {
            // Arrange
            var password = "MySecurePassword123!";

            // Act
            var keyInfo = AESProvider.DeriveKeyFromPassword(password, 256, 10000);

            // Assert
            Assert.NotNull(keyInfo);
            Assert.NotEmpty(keyInfo.Key);
            Assert.NotEmpty(keyInfo.IV);
            Assert.NotEmpty(keyInfo.Salt);
            Assert.Equal(256, keyInfo.KeySize);
            Assert.Equal(10000, keyInfo.Iterations);
        }

        [Fact]
        public void DeriveKeyFromPassword_WithSameSalt_ShouldProduceSameKey()
        {
            // Arrange
            var password = "TestPassword";
            var salt = new byte[32];
            new Random(42).NextBytes(salt); // Use fixed seed for reproducible salt

            // Act
            var keyInfo1 = AESProvider.DeriveKeyFromPassword(password, salt, 256, 1000);
            var keyInfo2 = AESProvider.DeriveKeyFromPassword(password, salt, 256, 1000);

            // Assert
            Assert.Equal(keyInfo1.Key, keyInfo2.Key);
            Assert.Equal(keyInfo1.IV, keyInfo2.IV);
        }

        [Fact]
        public void DeriveKeyFromPassword_WithDifferentSalt_ShouldProduceDifferentKey()
        {
            // Arrange
            var password = "TestPassword";

            // Act
            var keyInfo1 = AESProvider.DeriveKeyFromPassword(password, 256, 1000);
            var keyInfo2 = AESProvider.DeriveKeyFromPassword(password, 256, 1000);

            // Assert
            Assert.NotEqual(keyInfo1.Key, keyInfo2.Key);
            Assert.NotEqual(keyInfo1.Salt, keyInfo2.Salt);
        }

        [Fact]
        public void EncryptDecrypt_WithPasswordDerivedKey_ShouldWork()
        {
            // Arrange
            var password = "MyPassword123";
            var plainText = "Secret message to encrypt";
            var keyInfo = AESProvider.DeriveKeyFromPassword(password, 256, 5000);

            // Act
            using var aes = new AESProvider(keyInfo.Key, keyInfo.IV);
            var encryptedText = aes.EncryptString(plainText);
            var decryptedText = aes.DecryptString(encryptedText);

            // Assert
            Assert.Equal(plainText, decryptedText);
        }

        [Fact]
        public void GetKeyAndIV_ShouldReturnValidBase64()
        {
            // Act
            var key = _aesProvider!.GetKeyBase64();
            var iv = _aesProvider.GetIVBase64();

            // Assert
            Assert.NotEmpty(key);
            Assert.NotEmpty(iv);
            
            // Should be valid Base64
            var keyBytes = Convert.FromBase64String(key);
            var ivBytes = Convert.FromBase64String(iv);
            Assert.True(keyBytes.Length > 0);
            Assert.True(ivBytes.Length > 0);
        }

        [Fact]
        public void KeySize_ShouldReturnCorrectSize()
        {
            // Act
            var keySize = _aesProvider!.KeySize;

            // Assert
            Assert.Equal(256, keySize);
        }

        [Theory]
        [InlineData(128)]
        [InlineData(192)]
        [InlineData(256)]
        public void Constructor_WithDifferentKeySizes_ShouldWork(int keySize)
        {
            // Act
            using var aes = new AESProvider(keySize);

            // Assert
            Assert.Equal(keySize, aes.KeySize);
            
            // Test encryption/decryption
            var plainText = "Test message";
            var encrypted = aes.EncryptString(plainText);
            var decrypted = aes.DecryptString(encrypted);
            Assert.Equal(plainText, decrypted);
        }

        [Fact]
        public void EncryptDecrypt_WithBinaryData_ShouldWork()
        {
            // Arrange
            var originalData = new byte[] { 0x01, 0x02, 0x03, 0x04, 0x05, 0xFF, 0xFE, 0xFD };

            // Act
            var encryptedData = _aesProvider!.Encrypt(originalData);
            var decryptedData = _aesProvider.Decrypt(encryptedData);

            // Assert
            Assert.Equal(originalData, decryptedData);
        }

        [Fact]
        public void EncryptDecrypt_WithEmptyString_ShouldWork()
        {
            // Arrange
            var plainText = "";

            // Act
            var encryptedText = _aesProvider!.EncryptString(plainText);
            var decryptedText = _aesProvider.DecryptString(encryptedText);

            // Assert
            Assert.Equal(plainText, decryptedText);
        }

        [Fact]
        public void EncryptDecrypt_WithLargeData_ShouldWork()
        {
            // Arrange
            var largeText = new string('A', 10000); // 10KB of data

            // Act
            var encryptedText = _aesProvider!.EncryptString(largeText);
            var decryptedText = _aesProvider.DecryptString(encryptedText);

            // Assert
            Assert.Equal(largeText, decryptedText);
        }

        [Fact]
        public void Constructor_WithExistingKeyAndIV_ShouldWork()
        {
            // Arrange
            var keyInfo = AESProvider.GenerateKey(256);

            // Act
            using var aes = new AESProvider(keyInfo.Key, keyInfo.IV);
            var plainText = "Test with existing key";
            var encrypted = aes.EncryptString(plainText);
            var decrypted = aes.DecryptString(encrypted);

            // Assert
            Assert.Equal(plainText, decrypted);
            Assert.Equal(keyInfo.Key, aes.GetKeyBase64());
            Assert.Equal(keyInfo.IV, aes.GetIVBase64());
        }

        public void Dispose()
        {
            _aesProvider?.Dispose();
        }
    }
}
