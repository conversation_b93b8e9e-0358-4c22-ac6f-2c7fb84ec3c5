{"format": 1, "restore": {"D:\\00 Crack\\Tests\\LicenseGenerator.DeviceInfo.Tests\\LicenseGenerator.DeviceInfo.Tests.csproj": {}}, "projects": {"D:\\00 Crack\\LicenseGenerator.DeviceInfo\\LicenseGenerator.DeviceInfo.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\00 Crack\\LicenseGenerator.DeviceInfo\\LicenseGenerator.DeviceInfo.csproj", "projectName": "LicenseGenerator.DeviceInfo", "projectPath": "D:\\00 Crack\\LicenseGenerator.DeviceInfo\\LicenseGenerator.DeviceInfo.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\00 Crack\\LicenseGenerator.DeviceInfo\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["E:\\DevExpress 23.2\\Components\\Offline Packages", "e:\\DevExpress 24.2\\Components\\Offline Packages", "E:\\Microsoft Visual Studio\\Shared\\NuGetPackages", "D:\\Syncfusion\\Essential Studio\\WPF\\29.1.33\\ToolboxNuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\DevExpress 23.2.config", "C:\\Program Files (x86)\\NuGet\\Config\\DevExpress 24.2.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config", "C:\\Program Files (x86)\\NuGet\\Config\\Syncfusion Toolbox for WPF.config"], "originalTargetFrameworks": ["net6.0"], "sources": {"C:\\Program Files\\dotnet\\library-packs": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net6.0": {"targetAlias": "net6.0", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net6.0": {"targetAlias": "net6.0", "dependencies": {"System.Management": {"target": "Package", "version": "[9.0.6, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.300\\RuntimeIdentifierGraph.json"}}}, "D:\\00 Crack\\Tests\\LicenseGenerator.DeviceInfo.Tests\\LicenseGenerator.DeviceInfo.Tests.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\00 Crack\\Tests\\LicenseGenerator.DeviceInfo.Tests\\LicenseGenerator.DeviceInfo.Tests.csproj", "projectName": "LicenseGenerator.DeviceInfo.Tests", "projectPath": "D:\\00 Crack\\Tests\\LicenseGenerator.DeviceInfo.Tests\\LicenseGenerator.DeviceInfo.Tests.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\00 Crack\\Tests\\LicenseGenerator.DeviceInfo.Tests\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["E:\\DevExpress 23.2\\Components\\Offline Packages", "e:\\DevExpress 24.2\\Components\\Offline Packages", "E:\\Microsoft Visual Studio\\Shared\\NuGetPackages", "D:\\Syncfusion\\Essential Studio\\WPF\\29.1.33\\ToolboxNuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\DevExpress 23.2.config", "C:\\Program Files (x86)\\NuGet\\Config\\DevExpress 24.2.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config", "C:\\Program Files (x86)\\NuGet\\Config\\Syncfusion Toolbox for WPF.config"], "originalTargetFrameworks": ["net6.0"], "sources": {"C:\\Program Files\\dotnet\\library-packs": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net6.0": {"targetAlias": "net6.0", "projectReferences": {"D:\\00 Crack\\LicenseGenerator.DeviceInfo\\LicenseGenerator.DeviceInfo.csproj": {"projectPath": "D:\\00 Crack\\LicenseGenerator.DeviceInfo\\LicenseGenerator.DeviceInfo.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net6.0": {"targetAlias": "net6.0", "dependencies": {"Microsoft.NET.Test.Sdk": {"target": "Package", "version": "[17.1.0, )"}, "coverlet.collector": {"include": "Runtime, Build, Native, ContentFiles, Analyzers, BuildTransitive", "suppressParent": "All", "target": "Package", "version": "[3.1.2, )"}, "xunit": {"target": "Package", "version": "[2.4.1, )"}, "xunit.runner.visualstudio": {"include": "Runtime, Build, Native, ContentFiles, Analyzers, BuildTransitive", "suppressParent": "All", "target": "Package", "version": "[2.4.3, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.300\\RuntimeIdentifierGraph.json"}}}}}