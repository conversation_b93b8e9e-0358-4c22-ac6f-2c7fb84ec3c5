using System;
using System.ComponentModel.DataAnnotations;

namespace LicenseGenerator.Core.Models
{
    /// <summary>
    /// 加密密钥模型
    /// </summary>
    public class CryptoKeys
    {
        /// <summary>
        /// 密钥对唯一标识符
        /// </summary>
        public Guid Id { get; set; } = Guid.NewGuid();

        /// <summary>
        /// 密钥对名称
        /// </summary>
        [Required]
        [StringLength(100)]
        public string Name { get; set; } = string.Empty;

        /// <summary>
        /// RSA公钥 (PEM格式)
        /// </summary>
        [Required]
        public string PublicKey { get; set; } = string.Empty;

        /// <summary>
        /// RSA私钥 (PEM格式，加密存储)
        /// </summary>
        [Required]
        public string PrivateKey { get; set; } = string.Empty;

        /// <summary>
        /// 密钥长度 (位数)
        /// </summary>
        public int KeySize { get; set; } = 2048;

        /// <summary>
        /// 密钥算法类型
        /// </summary>
        public CryptoAlgorithm Algorithm { get; set; } = CryptoAlgorithm.RSA;

        /// <summary>
        /// 签名算法
        /// </summary>
        [StringLength(50)]
        public string SignatureAlgorithm { get; set; } = "SHA256withRSA";

        /// <summary>
        /// 密钥创建时间
        /// </summary>
        public DateTime CreatedAt { get; set; } = DateTime.UtcNow;

        /// <summary>
        /// 密钥过期时间 (null表示永不过期)
        /// </summary>
        public DateTime? ExpiryDate { get; set; }

        /// <summary>
        /// 是否为当前激活的密钥
        /// </summary>
        public bool IsActive { get; set; } = true;

        /// <summary>
        /// 密钥用途
        /// </summary>
        public KeyUsage Usage { get; set; } = KeyUsage.Signing;

        /// <summary>
        /// 密钥指纹 (公钥的SHA256哈希值)
        /// </summary>
        [StringLength(64)]
        public string Fingerprint { get; set; } = string.Empty;

        /// <summary>
        /// 创建者信息
        /// </summary>
        [StringLength(100)]
        public string CreatedBy { get; set; } = string.Empty;

        /// <summary>
        /// 密钥描述
        /// </summary>
        [StringLength(500)]
        public string Description { get; set; } = string.Empty;

        /// <summary>
        /// 是否受密码保护
        /// </summary>
        public bool IsPasswordProtected { get; set; } = false;

        /// <summary>
        /// 密钥状态
        /// </summary>
        public KeyStatus Status { get; set; } = KeyStatus.Active;

        /// <summary>
        /// 最后使用时间
        /// </summary>
        public DateTime? LastUsedAt { get; set; }

        /// <summary>
        /// 使用次数统计
        /// </summary>
        public long UsageCount { get; set; } = 0;

        /// <summary>
        /// 备注信息
        /// </summary>
        [StringLength(1000)]
        public string Notes { get; set; } = string.Empty;

        /// <summary>
        /// 检查密钥是否已过期
        /// </summary>
        public bool IsExpired => ExpiryDate.HasValue && ExpiryDate.Value < DateTime.UtcNow;

        /// <summary>
        /// 检查密钥是否可用
        /// </summary>
        public bool IsUsable => IsActive && Status == KeyStatus.Active && !IsExpired;

        /// <summary>
        /// 获取密钥摘要信息
        /// </summary>
        public string Summary => $"{Name} ({Algorithm} {KeySize}bit) - {Status}";
    }

    /// <summary>
    /// 加密算法枚举
    /// </summary>
    public enum CryptoAlgorithm
    {
        /// <summary>
        /// RSA算法
        /// </summary>
        RSA = 0,

        /// <summary>
        /// ECC椭圆曲线算法
        /// </summary>
        ECC = 1,

        /// <summary>
        /// DSA数字签名算法
        /// </summary>
        DSA = 2
    }

    /// <summary>
    /// 密钥用途枚举
    /// </summary>
    public enum KeyUsage
    {
        /// <summary>
        /// 数字签名
        /// </summary>
        Signing = 0,

        /// <summary>
        /// 数据加密
        /// </summary>
        Encryption = 1,

        /// <summary>
        /// 签名和加密
        /// </summary>
        SigningAndEncryption = 2,

        /// <summary>
        /// 密钥交换
        /// </summary>
        KeyExchange = 3
    }

    /// <summary>
    /// 密钥状态枚举
    /// </summary>
    public enum KeyStatus
    {
        /// <summary>
        /// 激活状态
        /// </summary>
        Active = 0,

        /// <summary>
        /// 已暂停
        /// </summary>
        Suspended = 1,

        /// <summary>
        /// 已撤销
        /// </summary>
        Revoked = 2,

        /// <summary>
        /// 已过期
        /// </summary>
        Expired = 3,

        /// <summary>
        /// 已损坏
        /// </summary>
        Compromised = 4
    }
}
